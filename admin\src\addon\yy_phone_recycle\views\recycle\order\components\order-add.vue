<template>
    <el-dialog
        v-model="showDialog"
        title="添加回收订单"
        width="800px"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            v-loading="loading"
        >
            <!-- 基本信息 -->
            <div class="form-section">
                <h4 class="section-title">基本信息</h4>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="门店" prop="store_id">
                            <el-select v-model="formData.store_id" placeholder="请选择门店" class="w-full">
                                <el-option
                                    v-for="store in storeOptions"
                                    :key="store.id"
                                    :label="store.store_name"
                                    :value="store.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手机型号" prop="phone_model_id">
                            <el-select v-model="formData.phone_model_id" placeholder="请选择手机型号" class="w-full" @change="handlePhoneModelChange">
                                <el-option
                                    v-for="model in phoneModelOptions"
                                    :key="model.id"
                                    :label="`${model.brand_name} ${model.series_name} ${model.model_name}`"
                                    :value="model.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="评估模板" prop="evaluation_template_id">
                            <el-select v-model="formData.evaluation_template_id" placeholder="请选择评估模板" class="w-full">
                                <el-option
                                    v-for="template in evaluationTemplateOptions"
                                    :key="template.id"
                                    :label="template.template_name"
                                    :value="template.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="预估价格" prop="estimated_price">
                            <el-input-number
                                v-model="formData.estimated_price"
                                :min="0"
                                :precision="2"
                                placeholder="请输入预估价格"
                                class="w-full"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 客户信息 -->
            <div class="form-section">
                <h4 class="section-title">客户信息</h4>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="客户姓名" prop="customer_name">
                            <el-input v-model="formData.customer_name" placeholder="请输入客户姓名" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="客户手机" prop="customer_phone">
                            <el-input v-model="formData.customer_phone" placeholder="请输入客户手机号" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="身份证号" prop="customer_id_card">
                            <el-input v-model="formData.customer_id_card" placeholder="请输入身份证号（可选）" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 手机信息 -->
            <div class="form-section">
                <h4 class="section-title">手机信息</h4>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="手机IMEI" prop="phone_imei">
                            <el-input v-model="formData.phone_imei" placeholder="请输入手机IMEI（可选）" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手机颜色" prop="phone_color">
                            <el-input v-model="formData.phone_color" placeholder="请输入手机颜色" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="存储容量" prop="phone_storage">
                            <el-input v-model="formData.phone_storage" placeholder="请输入存储容量" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="支付方式" prop="payment_method">
                            <el-select v-model="formData.payment_method" placeholder="请选择支付方式" class="w-full">
                                <el-option label="现金" :value="1" />
                                <el-option label="银行转账" :value="2" />
                                <el-option label="支付宝" :value="3" />
                                <el-option label="微信" :value="4" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="formData.payment_method > 1">
                    <el-col :span="24">
                        <el-form-item label="支付账户" prop="payment_account">
                            <el-input v-model="formData.payment_account" placeholder="请输入支付账户" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 备注信息 -->
            <div class="form-section">
                <h4 class="section-title">备注信息</h4>
                <el-form-item label="备注" prop="remarks">
                    <el-input
                        v-model="formData.remarks"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入备注信息（可选）"
                    />
                </el-form-item>
            </div>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createRecycleOrder } from '@/addon/yy_phone_recycle/api/recycle'
import { getStoreOptions } from '@/addon/yy_phone_recycle/api/store'
import { getPhoneModelOptions } from '@/addon/yy_phone_recycle/api/phone'
import { getEvaluationTemplateOptions } from '@/addon/yy_phone_recycle/api/evaluation'

interface Props {
    modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'complete'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const showDialog = ref(false)

// 选项数据
const storeOptions = ref([])
const phoneModelOptions = ref([])
const evaluationTemplateOptions = ref([])

// 表单数据
const formData = reactive({
    store_id: 0,
    phone_model_id: 0,
    evaluation_template_id: 0,
    customer_name: '',
    customer_phone: '',
    customer_id_card: '',
    phone_imei: '',
    phone_color: '',
    phone_storage: '',
    estimated_price: 0,
    payment_method: 1,
    payment_account: '',
    remarks: ''
})

// 表单验证规则
const formRules = {
    store_id: [
        { required: true, message: '请选择门店', trigger: 'change' }
    ],
    phone_model_id: [
        { required: true, message: '请选择手机型号', trigger: 'change' }
    ],
    evaluation_template_id: [
        { required: true, message: '请选择评估模板', trigger: 'change' }
    ],
    customer_name: [
        { required: true, message: '请输入客户姓名', trigger: 'blur' },
        { max: 50, message: '客户姓名长度不能超过50个字符', trigger: 'blur' }
    ],
    customer_phone: [
        { required: true, message: '请输入客户手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    customer_id_card: [
        { pattern: /^[1-9]\d{17}$/, message: '请输入正确的身份证号', trigger: 'blur' }
    ],
    estimated_price: [
        { required: true, message: '请输入预估价格', trigger: 'blur' }
    ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
    showDialog.value = val
    if (val) {
        loadOptions()
    }
})

watch(showDialog, (val) => {
    emit('update:modelValue', val)
})

/**
 * 加载选项数据
 */
const loadOptions = async () => {
    loading.value = true
    try {
        const [storeRes, phoneModelRes, templateRes] = await Promise.all([
            getStoreOptions(),
            getPhoneModelOptions(),
            getEvaluationTemplateOptions()
        ])
        
        storeOptions.value = storeRes.data
        phoneModelOptions.value = phoneModelRes.data
        evaluationTemplateOptions.value = templateRes.data
    } catch (error) {
        console.error('加载选项数据失败:', error)
        ElMessage.error('加载数据失败')
    } finally {
        loading.value = false
    }
}

/**
 * 手机型号变化处理
 */
const handlePhoneModelChange = (modelId: number) => {
    // 可以根据手机型号自动设置评估模板
    // 这里可以添加相关逻辑
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
    if (!formRef.value) return
    
    try {
        await formRef.value.validate()
        submitting.value = true
        
        await createRecycleOrder(formData)
        ElMessage.success('添加订单成功')
        emit('complete')
        handleClose()
    } catch (error) {
        console.error('添加订单失败:', error)
    } finally {
        submitting.value = false
    }
}

/**
 * 关闭弹窗
 */
const handleClose = () => {
    showDialog.value = false
    // 重置表单
    if (formRef.value) {
        formRef.value.resetFields()
    }
    Object.keys(formData).forEach(key => {
        if (typeof formData[key] === 'number') {
            formData[key] = 0
        } else {
            formData[key] = ''
        }
    })
    formData.payment_method = 1
}
</script>

<style lang="scss" scoped>
.form-section {
    margin-bottom: 20px;
    
    .section-title {
        margin: 0 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #e4e7ed;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
    }
}

.dialog-footer {
    text-align: right;
}

.w-full {
    width: 100%;
}
</style>
