<template>
    <div class="area-component">
        <!-- 省 -->
        <el-select :placeholder="t('provincePlaceholder')" v-model="state.province" clearable @change="changeArea('province')">
            <el-option v-for="item in state.provinceList" :key="item.id" :label="item.name" :value="item.id"/>
        </el-select>
        <!-- 市 -->
        <el-select :placeholder="t('cityPlaceholder')" style="margin: 0 10px;" :disabled="!state.province" v-model="state.city" clearable @change="changeArea('city')">
            <el-option v-for="item in state.citiesList" :key="item.id" :label="item.name" :value="item.id"/>
        </el-select>
        <!-- 区 -->
        <el-select :placeholder="t('districtPlaceholder')" :disabled="!state.province || !state.city" v-model="state.area" clearable @change="changeArea('area')">
            <el-option v-for="item in state.areasList" :key="item.id" :label="item.name" :value="item.id"/>
        </el-select>
    </div>
</template>

<script lang="ts" setup>
import { reactive, onBeforeMount, watch } from 'vue'
import { getAreaListByPid } from '@/app/api/sys'
import { t } from '@/lang'

// 定义数据类型
export interface areaType {
  id: string
  name: string
  pid: number
  children?: areaType[]
}

const prop = defineProps({
    initData: {
        type: Object,
        default: {
            province: '',
            city: '',
            area: ''
        }
    }
})

const emits = defineEmits<{(e: 'areaChange', value: any):void}>()

const state = reactive({
    // 用于展示的省市区数据
    provinceList: [] as areaType[],
    citiesList: [] as areaType[],
    areasList: [] as areaType[],
    // 最终选择的省市区
    province: '',
    city: '',
    area: ''
})

onBeforeMount(async () => {
    state.provinceList = (await getAreaListByPid(0)).data
})

// 使用一个标志来避免循环更新
let isInternalUpdate = false
let isInitialized = false

watch(() => prop.initData, async (newVal, oldVal) => {
    console.log('🔍 地址组件接收数据:', newVal)

    // 避免重复初始化
    if (JSON.stringify(newVal) === JSON.stringify(oldVal)) {
        console.log('⏭️ 数据未变化，跳过初始化')
        return
    }

    // 重置初始化状态
    isInitialized = false

    if (newVal && (newVal.province || newVal.city || newVal.area)) {
        isInternalUpdate = true

        // 设置选中的值
        state.province = newVal.province || ''
        state.city = newVal.city || ''
        state.area = newVal.area || ''

        console.log('✅ 设置地址状态:', {
            province: state.province,
            city: state.city,
            area: state.area
        })

        try {
            // 按顺序加载省市区数据
            if (state.province) {
                console.log('🏙️ 加载城市列表，省份ID:', state.province)
                const cityRes = await getAreaListByPid(parseInt(state.province))
                state.citiesList = cityRes.data || []
                console.log('✅ 城市列表加载完成:', state.citiesList.length, '个')

                if (state.city) {
                    console.log('🏘️ 加载区县列表，城市ID:', state.city)
                    const areaRes = await getAreaListByPid(parseInt(state.city))
                    state.areasList = areaRes.data || []
                    console.log('✅ 区县列表加载完成:', state.areasList.length, '个')
                }
            }

            isInitialized = true
            console.log('🎉 地址组件初始化完成')
        } catch (error) {
            console.error('❌ 初始化地址数据失败:', error)
        } finally {
            // 延迟重置标志，避免影响用户操作
            setTimeout(() => {
                isInternalUpdate = false
                console.log('🔓 解除内部更新锁定')
            }, 200)
        }
    } else {
        // 如果没有初始数据，清空所有选择
        console.log('🧹 清空地址选择')
        state.province = ''
        state.city = ''
        state.area = ''
        state.citiesList = []
        state.areasList = []
        isInitialized = true
        isInternalUpdate = false
    }
}, { deep: true, immediate: true })

// 移除这些watch，避免重复触发，改为在changeArea中统一处理

const emitsArea = () => {
    const paramsData = {
        province: {} as areaType,
        city: {} as areaType,
        area: {} as areaType
    }
    let tmp = state.provinceList.find((item) => item.id === state.province)
    paramsData.province.name = tmp ? tmp.name : ''
    paramsData.province.id = tmp ? tmp.id : ''
    tmp = state.citiesList.find((item) => item.id === state.city) as any
    paramsData.city.name = tmp ? tmp.name : ''
    paramsData.city.id = tmp ? tmp.id : ''
    tmp = state.areasList.find((item) => item.id === state.area) as any
    paramsData.area.name = tmp ? tmp.name : ''
    paramsData.area.id = tmp ? tmp.id : ''

    console.log('地址组件发送数据:', paramsData, '当前状态:', {
        province: state.province,
        city: state.city,
        area: state.area,
        provinceListLength: state.provinceList.length,
        citiesListLength: state.citiesList.length,
        areasListLength: state.areasList.length
    })

    emits('areaChange', paramsData)
}

const changeArea = async (data : any) => {
    // 如果是内部更新或者还没初始化完成，不处理
    if (isInternalUpdate || !isInitialized) {
        console.log('跳过地址变化处理:', { isInternalUpdate, isInitialized })
        return
    }

    console.log('地址选择变化:', data, '当前值:', {
        province: state.province,
        city: state.city,
        area: state.area
    })

    if (data == 'province') {
        // 清空下级选择
        state.city = ''
        state.area = ''
        state.citiesList = []
        state.areasList = []

        if (!state.province) {
            console.log('省份为空，触发事件')
            emitsArea()
            return
        }

        try {
            console.log('省份变化，开始加载城市列表，省份ID:', state.province)
            const cityRes = await getAreaListByPid(parseInt(state.province))
            state.citiesList = cityRes.data || []
            console.log('省份变化，加载城市完成:', state.citiesList.length, '个')
            emitsArea()
        } catch (error) {
            console.error('获取城市列表失败:', error)
        }
    }

    if (data == 'city') {
        // 清空下级选择
        state.area = ''
        state.areasList = []

        if (!state.city) {
            console.log('城市为空，触发事件')
            emitsArea()
            return
        }

        try {
            console.log('城市变化，开始加载区县列表，城市ID:', state.city)
            const areaRes = await getAreaListByPid(parseInt(state.city))
            state.areasList = areaRes.data || []
            console.log('城市变化，加载区县完成:', state.areasList.length, '个')
            emitsArea()
        } catch (error) {
            console.error('获取区县列表失败:', error)
        }
    }

    if (data == 'area') {
        console.log('区县选择:', state.area)
        emitsArea()
    }
}

</script>

<style lang="scss" scoped>

</style>
