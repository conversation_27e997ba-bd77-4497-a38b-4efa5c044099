<template>
    <div class="area-component">
        <!-- 省 -->
        <el-select :placeholder="t('provincePlaceholder')" v-model="state.province" clearable @change="changeArea('province')">
            <el-option v-for="item in state.provinceList" :key="item.id" :label="item.name" :value="item.id"/>
        </el-select>
        <!-- 市 -->
        <el-select :placeholder="t('cityPlaceholder')" style="margin: 0 10px;" :disabled="!state.province" v-model="state.city" clearable @change="changeArea('city')">
            <el-option v-for="item in state.citiesList" :key="item.id" :label="item.name" :value="item.id"/>
        </el-select>
        <!-- 区 -->
        <el-select :placeholder="t('districtPlaceholder')" :disabled="!state.province || !state.city" v-model="state.area" clearable @change="changeArea('area')">
            <el-option v-for="item in state.areasList" :key="item.id" :label="item.name" :value="item.id"/>
        </el-select>
    </div>
</template>

<script lang="ts" setup>
import { reactive, onBeforeMount, watch } from 'vue'
import { getAreaListByPid } from '@/app/api/sys'
import { t } from '@/lang'

// 定义数据类型
export interface areaType {
  id: string
  name: string
  pid: number
  children?: areaType[]
}

const prop = defineProps({
    initData: {
        type: Object,
        default: {
            province: '',
            city: '',
            area: ''
        }
    }
})

const emits = defineEmits<{(e: 'areaChange', value: any):void}>()

const state = reactive({
    // 用于展示的省市区数据
    provinceList: [] as areaType[],
    citiesList: [] as areaType[],
    areasList: [] as areaType[],
    // 最终选择的省市区
    province: '',
    city: '',
    area: ''
})

onBeforeMount(async () => {
    state.provinceList = (await getAreaListByPid(0)).data
})

// 使用一个标志来避免循环更新
let isInternalUpdate = false
let isInitialized = false

watch(() => prop.initData, async (newVal, oldVal) => {
    // 避免重复初始化
    if (JSON.stringify(newVal) === JSON.stringify(oldVal)) {
        return
    }

    // 重置初始化状态
    isInitialized = false

    if (newVal && (newVal.province || newVal.city || newVal.area)) {
        isInternalUpdate = true

        // 设置选中的值，确保数据类型匹配
        state.province = newVal.province ? parseInt(newVal.province) : ''
        state.city = newVal.city ? parseInt(newVal.city) : ''
        state.area = newVal.area ? parseInt(newVal.area) : ''

        try {
            // 按顺序加载省市区数据
            if (state.province) {
                const cityRes = await getAreaListByPid(parseInt(state.province))
                state.citiesList = cityRes.data || []

                if (state.city) {
                    const areaRes = await getAreaListByPid(parseInt(state.city))
                    state.areasList = areaRes.data || []
                }
            }

            isInitialized = true
        } catch (error) {
            console.error('初始化地址数据失败:', error)
        } finally {
            // 延迟重置标志，避免影响用户操作
            setTimeout(() => {
                isInternalUpdate = false
            }, 200)
        }
    } else {
        // 如果没有初始数据，清空所有选择
        state.province = ''
        state.city = ''
        state.area = ''
        state.citiesList = []
        state.areasList = []
        isInitialized = true
        isInternalUpdate = false
    }
}, { deep: true, immediate: true })

// 移除这些watch，避免重复触发，改为在changeArea中统一处理

const emitsArea = () => {
    const paramsData = {
        province: {} as areaType,
        city: {} as areaType,
        area: {} as areaType
    }

    // 使用 == 而不是 === 来比较，避免类型问题
    let tmp = state.provinceList.find((item) => item.id == state.province)
    paramsData.province.name = tmp ? tmp.name : ''
    paramsData.province.id = tmp ? tmp.id : ''

    tmp = state.citiesList.find((item) => item.id == state.city) as any
    paramsData.city.name = tmp ? tmp.name : ''
    paramsData.city.id = tmp ? tmp.id : ''

    tmp = state.areasList.find((item) => item.id == state.area) as any
    paramsData.area.name = tmp ? tmp.name : ''
    paramsData.area.id = tmp ? tmp.id : ''

    emits('areaChange', paramsData)
}

const changeArea = async (data : any) => {
    // 如果是内部更新或者还没初始化完成，不处理
    if (isInternalUpdate || !isInitialized) {
        return
    }

    if (data == 'province') {
        // 清空下级选择
        state.city = ''
        state.area = ''
        state.citiesList = []
        state.areasList = []

        if (!state.province) {
            emitsArea()
            return
        }

        try {
            const cityRes = await getAreaListByPid(parseInt(state.province))
            state.citiesList = cityRes.data || []
            emitsArea()
        } catch (error) {
            console.error('获取城市列表失败:', error)
        }
    }

    if (data == 'city') {
        // 清空下级选择
        state.area = ''
        state.areasList = []

        if (!state.city) {
            emitsArea()
            return
        }

        try {
            const areaRes = await getAreaListByPid(parseInt(state.city))
            state.areasList = areaRes.data || []
            emitsArea()
        } catch (error) {
            console.error('获取区县列表失败:', error)
        }
    }

    if (data == 'area') {
        emitsArea()
    }
}

</script>

<style lang="scss" scoped>

</style>
