<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\service\admin\evaluation;

use addon\yy_phone_recycle\app\model\evaluation\EvaluationTemplate;
use addon\yy_phone_recycle\app\model\evaluation\TemplateQuestion;
use addon\yy_phone_recycle\app\model\evaluation\TemplateOption;
use addon\yy_phone_recycle\app\model\PhoneModel;
use core\base\BaseAdminService;
use core\exception\AdminException;
use think\facade\Db;

/**
 * 估价模板服务层
 * Class EvaluationTemplateService
 * @package addon\yy_phone_recycle\app\service\admin\evaluation
 */
class EvaluationTemplateService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new EvaluationTemplate();
    }

    /**
     * 获取估价模板列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id,name,model_id,base_price,min_price,description,first_question_id,total_questions,avg_completion_time,completion_rate,status,sort,created_by,updated_by,create_time,update_time';
        $order = 'sort desc, id desc';

        $search_model = $this->model
            ->withSearch(["name", "model_id", "status"], $where)
            ->with(['phoneModel' => function($query) {
                $query->field('id,name,image');
            }])
            ->field($field)
            ->order($order);

        // 处理题目数量筛选
        if (isset($where['question_count_filter']) && $where['question_count_filter'] !== '') {
            // 调试信息
            error_log("Question count filter: " . $where['question_count_filter']);

            if ($where['question_count_filter'] === 'no_questions') {
                // 筛选无题目的模板
                $template_ids_with_questions = (new \addon\yy_phone_recycle\app\model\evaluation\TemplateQuestion())
                    ->where('status', 1)
                    ->column('template_id');

                error_log("Template IDs with questions: " . json_encode($template_ids_with_questions));

                if (!empty($template_ids_with_questions)) {
                    $search_model = $search_model->where('id', 'not in', array_unique($template_ids_with_questions));
                }
            } elseif ($where['question_count_filter'] === 'has_questions') {
                // 筛选有题目的模板
                $template_ids_with_questions = (new \addon\yy_phone_recycle\app\model\evaluation\TemplateQuestion())
                    ->where('status', 1)
                    ->column('template_id');

                error_log("Template IDs with questions: " . json_encode($template_ids_with_questions));

                if (!empty($template_ids_with_questions)) {
                    $search_model = $search_model->where('id', 'in', array_unique($template_ids_with_questions));
                } else {
                    // 如果没有任何模板有题目，返回空结果
                    $search_model = $search_model->where('id', '=', 0);
                }
            }
        }

        // 检查是否需要分页
        $limit = $this->request->param('limit', 15);
        if ($limit > 120) {
            // 如果limit超过120，返回所有数据（不分页）
            $data = $search_model->select()->toArray();
            $list = [
                'data' => $data,
                'total' => count($data),
                'page' => 1,
                'limit' => count($data)
            ];
        } else {
            $list = $this->pageQuery($search_model);
        }

        // 处理数据，添加template_name和model_name字段，并计算题目数量和完成率
        if (!empty($list['data'])) {
            foreach ($list['data'] as &$item) {
                $item['template_name'] = $item['name'];
                if ($item['phoneModel']) {
                    $item['model_name'] = $item['phoneModel']['name'];
                } else {
                    $item['model_name'] = '未知型号';
                }

                // 动态计算题目数量
                $question_count = (new \addon\yy_phone_recycle\app\model\evaluation\TemplateQuestion())
                    ->where('template_id', $item['id'])
                    ->where('status', 1)
                    ->count();
                $item['total_questions'] = $question_count;

                // 动态计算完成率
                $total_records = (new \addon\yy_phone_recycle\app\model\evaluation\EvaluationRecord())
                    ->where('template_id', $item['id'])
                    ->count();

                if ($total_records > 0) {
                    $completed_records = (new \addon\yy_phone_recycle\app\model\evaluation\EvaluationRecord())
                        ->where('template_id', $item['id'])
                        ->where('completion_status', 1)
                        ->count();
                    $item['completion_rate'] = round($completed_records / $total_records * 100, 2);
                } else {
                    $item['completion_rate'] = 0;
                }
            }
        }

        return $list;
    }

    /**
     * 获取估价模板信息
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = 'id,name,model_id,base_price,min_price,description,first_question_id,total_questions,avg_completion_time,completion_rate,status,sort,created_by,updated_by,create_time,update_time';

        $info = $this->model->field($field)
            ->with(['phoneModel' => function($query) {
                $query->field('id,name,image,brand_id,series_id');
            }])
            ->where([['id', '=', $id]])
            ->findOrEmpty()->toArray();

        if (empty($info)) {
            throw new AdminException('EVALUATION_TEMPLATE_NOT_EXIST');
        }

        return $info;
    }

    /**
     * 添加估价模板
     * @param array $data
     * @return mixed
     */
    public function add(array $data)
    {
        // 验证手机型号是否存在
        $phone_model = (new \addon\yy_phone_recycle\app\model\PhoneModel())->where('id', $data['model_id'])->findOrEmpty();
        if ($phone_model->isEmpty()) {
            throw new AdminException('PHONE_MODEL_NOT_EXIST');
        }

        // 检查该型号是否已有模板
        $exists = $this->model->where('model_id', $data['model_id'])->findOrEmpty();
        if (!$exists->isEmpty()) {
            throw new AdminException('EVALUATION_TEMPLATE_ALREADY_EXIST');
        }

        $data['name'] = $data['name'] ?: $phone_model['name'] . '估价模板';
        $data['created_by'] = $this->uid;
        $data['updated_by'] = $this->uid;

        $res = $this->model->create($data);
        return $res->id;
    }

    /**
     * 编辑估价模板
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function edit(int $id, array $data)
    {
        $info = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($info->isEmpty()) {
            throw new AdminException('EVALUATION_TEMPLATE_NOT_EXIST');
        }

        // 如果修改了型号，检查新型号是否已有模板
        if (isset($data['model_id']) && $data['model_id'] != $info['model_id']) {
            $exists = $this->model->where('model_id', $data['model_id'])->where('id', '<>', $id)->findOrEmpty();
            if (!$exists->isEmpty()) {
                throw new AdminException('EVALUATION_TEMPLATE_ALREADY_EXIST');
            }
        }

        $data['updated_by'] = $this->uid;

        $res = $info->save($data);
        return $res;
    }

    /**
     * 删除估价模板
     * @param int $id
     * @return bool
     */
    public function del(int $id)
    {
        $info = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($info->isEmpty()) {
            throw new AdminException('EVALUATION_TEMPLATE_NOT_EXIST');
        }

        Db::startTrans();
        try {
            // 删除模板相关的题目和选项
            $template_questions = (new TemplateQuestion())->where('template_id', $id)->select();
            foreach ($template_questions as $question) {
                (new TemplateOption())->where('template_question_id', $question['id'])->delete();
            }
            (new TemplateQuestion())->where('template_id', $id)->delete();

            // 删除模板
            $res = $info->delete();
            
            Db::commit();
            return $res;
        } catch (\Exception $e) {
            Db::rollback();
            throw new AdminException($e->getMessage());
        }
    }

    /**
     * 复制估价模板
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function copy(int $id, array $data)
    {
        $template = $this->model->where('id', $id)->findOrEmpty();
        if ($template->isEmpty()) {
            throw new AdminException('EVALUATION_TEMPLATE_NOT_EXIST');
        }

        // 验证目标型号
        $phone_model = (new \addon\yy_phone_recycle\app\model\PhoneModel())->where('id', $data['model_id'])->findOrEmpty();
        if ($phone_model->isEmpty()) {
            throw new AdminException('PHONE_MODEL_NOT_EXIST');
        }

        // 检查目标型号是否已有模板
        $exists = $this->model->where('model_id', $data['model_id'])->findOrEmpty();
        if (!$exists->isEmpty()) {
            throw new AdminException('EVALUATION_TEMPLATE_ALREADY_EXIST');
        }

        Db::startTrans();
        try {
            // 复制模板基本信息
            $new_template_data = $template->toArray();
            unset($new_template_data['id']);
            $new_template_data['name'] = $data['name'] ?: $phone_model['name'] . '估价模板';
            $new_template_data['model_id'] = $data['model_id'];
            $new_template_data['created_by'] = $this->uid;
            $new_template_data['updated_by'] = $this->uid;

            $new_template = $this->model->create($new_template_data);

            // 复制题目和选项
            $this->copyTemplateQuestions($id, $new_template->id);

            Db::commit();
            return $new_template->id;
        } catch (\Exception $e) {
            Db::rollback();
            throw new AdminException($e->getMessage());
        }
    }

    /**
     * 复制模板题目和选项
     * @param int $source_template_id
     * @param int $target_template_id
     */
    private function copyTemplateQuestions(int $source_template_id, int $target_template_id)
    {
        $questions = (new TemplateQuestion())->where('template_id', $source_template_id)->order('sort asc')->select();
        $question_id_map = []; // 用于映射旧题目ID到新题目ID

        foreach ($questions as $question) {
            $new_question_data = $question->toArray();
            unset($new_question_data['id']);
            $new_question_data['template_id'] = $target_template_id;


            $new_question = (new TemplateQuestion())->create($new_question_data);
            $question_id_map[$question['id']] = $new_question->id;

            // 复制选项
            $options = (new TemplateOption())->where('template_question_id', $question['id'])->order('sort asc')->select();
            foreach ($options as $option) {
                $new_option_data = $option->toArray();
                unset($new_option_data['id']);
                $new_option_data['template_question_id'] = $new_question->id;


                (new TemplateOption())->create($new_option_data);
            }
        }

        // 更新题目间的跳转关系
        foreach ($questions as $question) {
            $options = (new TemplateOption())->where('template_question_id', $question_id_map[$question['id']])->select();
            foreach ($options as $option) {
                if ($option['next_question_id'] && isset($question_id_map[$option['next_question_id']])) {
                    $option->save(['next_question_id' => $question_id_map[$option['next_question_id']]]);
                }
            }
        }
    }

    /**
     * 修改模板状态
     * @param int $id
     * @param int $status
     * @return bool
     */
    public function modifyStatus(int $id, int $status)
    {
        $info = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($info->isEmpty()) {
            throw new AdminException('EVALUATION_TEMPLATE_NOT_EXIST');
        }

        $data = [
            'status' => $status,
            'updated_by' => $this->uid
        ];

        return $info->save($data);
    }

    /**
     * 获取手机型号列表（用于模板创建）
     * @return array
     */
    public function getPhoneModelList()
    {
        $phone_model = new \addon\yy_phone_recycle\app\model\PhoneModel();
        $list = $phone_model->field('id,name,image,brand_id,series_id')
            ->with(['brand' => function($query) {
                $query->field('id,name');
            }, 'series' => function($query) {
                $query->field('id,name');
            }])
            ->where('status', 1)
            ->order('sort desc, id desc')
            ->select()
            ->toArray();

        return $list;
    }

    /**
     * 获取可复制的模板列表
     * @param int $exclude_model_id 排除的型号ID
     * @return array
     */
    public function getCopyableTemplates(int $exclude_model_id = 0)
    {
        $where = [['status', '=', 1]];
        if ($exclude_model_id > 0) {
            $where[] = ['model_id', '<>', $exclude_model_id];
        }

        $list = $this->model->field('id,name,model_id,base_price,total_questions')
            ->with(['phoneModel' => function($query) {
                $query->field('id,name');
            }])
            ->where($where)
            ->order('id desc')
            ->select()
            ->toArray();

        return $list;
    }

    /**
     * 获取模板统计信息
     * @param int $id
     * @return array
     */
    public function getStatistics(int $id)
    {
        $template = $this->model->where('id', $id)->findOrEmpty();
        if ($template->isEmpty()) {
            throw new AdminException('EVALUATION_TEMPLATE_NOT_EXIST');
        }

        // 获取题目数量
        $question_count = (new TemplateQuestion())->where('template_id', $id)->where('status', 1)->count();

        // 获取选项数量
        $option_count = (new TemplateOption())
            ->alias('o')
            ->join('yy_phone_evaluation_template_question q', 'o.template_question_id = q.id')
            ->where('q.template_id', $id)
            ->where('o.status', 1)
            ->count();

        // 获取估价记录统计
        $record_model = new \addon\yy_phone_recycle\app\model\evaluation\EvaluationRecord();
        $total_records = $record_model->where('template_id', $id)->count();
        $completed_records = $record_model->where('template_id', $id)->where('completion_status', 1)->count();
        $completion_rate = $total_records > 0 ? round($completed_records / $total_records * 100, 2) : 0;

        // 获取平均估价
        $avg_price = $record_model->where('template_id', $id)->where('completion_status', 1)->avg('final_price') ?: 0;

        return [
            'question_count' => $question_count,
            'option_count' => $option_count,
            'total_records' => $total_records,
            'completed_records' => $completed_records,
            'completion_rate' => $completion_rate,
            'avg_price' => round($avg_price, 2),
        ];
    }

    /**
     * 导出模板配置
     * @param int $id
     * @return array
     */
    public function exportTemplate(int $id)
    {
        $template = $this->getInfo($id);

        // 获取题目和选项
        $questions = (new TemplateQuestion())
            ->where('template_id', $id)
            ->with(['questionLibrary', 'templateOptions' => function($query) {
                $query->with('optionLibrary')->order('sort asc');
            }])
            ->order('sort asc')
            ->select()
            ->toArray();

        $template['questions'] = $questions;
        $template['export_time'] = date('Y-m-d H:i:s');

        return $template;
    }

    /**
     * 导入模板配置
     * @param array $template_data
     * @param int $model_id
     * @return mixed
     */
    public function importTemplate(array $template_data, int $model_id)
    {
        // 验证目标型号
        $phone_model = (new \addon\yy_phone_recycle\app\model\PhoneModel())->where('id', $model_id)->findOrEmpty();
        if ($phone_model->isEmpty()) {
            throw new AdminException('PHONE_MODEL_NOT_EXIST');
        }

        // 检查目标型号是否已有模板
        $exists = $this->model->where('model_id', $model_id)->findOrEmpty();
        if (!$exists->isEmpty()) {
            throw new AdminException('EVALUATION_TEMPLATE_ALREADY_EXIST');
        }

        Db::startTrans();
        try {
            // 创建新模板
            $new_template_data = [
                'name' => $phone_model['name'] . '估价模板',
                'model_id' => $model_id,
                'base_price' => $template_data['base_price'] ?? 0,
                'min_price' => $template_data['min_price'] ?? 0,
                'description' => $template_data['description'] ?? '',
                'total_questions' => $template_data['total_questions'] ?? 0,
                'status' => 1,
                'sort' => 0,
                'created_by' => $this->uid,
                'updated_by' => $this->uid,
            ];

            $new_template = $this->model->create($new_template_data);

            // 导入题目和选项
            if (isset($template_data['questions']) && is_array($template_data['questions'])) {
                $this->importTemplateQuestions($template_data['questions'], $new_template->id);
            }

            Db::commit();
            return $new_template->id;
        } catch (\Exception $e) {
            Db::rollback();
            throw new AdminException($e->getMessage());
        }
    }

    /**
     * 导入模板题目和选项
     * @param array $questions
     * @param int $template_id
     */
    private function importTemplateQuestions(array $questions, int $template_id)
    {
        foreach ($questions as $question_data) {
            $new_question_data = [
                'template_id' => $template_id,
                'question_library_id' => $question_data['question_library_id'] ?? 0,
                'question_title' => $question_data['question_title'] ?? '',
                'question_description' => $question_data['question_description'] ?? '',
                'layout_config' => $question_data['layout_config'] ?? null,
                'validation_rules' => $question_data['validation_rules'] ?? null,
                'sort' => $question_data['sort'] ?? 0,
                'status' => 1,
            ];

            $new_question = (new TemplateQuestion())->create($new_question_data);

            // 导入选项
            if (isset($question_data['template_options']) && is_array($question_data['template_options'])) {
                foreach ($question_data['template_options'] as $option_data) {
                    $new_option_data = [
                        'template_question_id' => $new_question->id,
                        'option_library_id' => $option_data['option_library_id'] ?? 0,
                        'option_text' => $option_data['option_text'] ?? '',
                        'option_value' => $option_data['option_value'] ?? '',
                        'price_deduction' => $option_data['price_deduction'] ?? 0,
                        'deduction_type' => $option_data['deduction_type'] ?? 1,
                        'is_final' => $option_data['is_final'] ?? 0,
                        'next_question_id' => null, // 导入时先不设置跳转关系
                        'display_config' => $option_data['display_config'] ?? null,
                        'condition_rules' => $option_data['condition_rules'] ?? null,
                        'sort' => $option_data['sort'] ?? 0,
                        'status' => 1,
                    ];

                    (new TemplateOption())->create($new_option_data);
                }
            }
        }
    }

    /**
     * 获取评估模板选项
     * @return array
     */
    public function getOptions()
    {
        $list = $this->model->where('status', 1)
            ->field('id, template_name, phone_model_id')
            ->with(['phoneModel' => function($query) {
                $query->field('id, model_name, brand_id')->with(['brand' => function($q) {
                    $q->field('id, brand_name');
                }]);
            }])
            ->select()
            ->toArray();

        return $list;
    }
}
