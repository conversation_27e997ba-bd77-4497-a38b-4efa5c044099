<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <span class="text-page-title">回收订单管理</span>
                <el-button type="primary" @click="handleAdd">
                    <template #icon>
                        <icon name="element-Plus" />
                    </template>
                    添加订单
                </el-button>
            </div>

            <!-- 搜索表单 -->
            <el-card class="box-card !border-none my-[10px] table-search-wrap" shadow="never">
                <el-form :inline="true" :model="searchForm" ref="searchFormRef">
                    <el-form-item label="订单编号">
                        <el-input v-model="searchForm.order_no" placeholder="请输入订单编号" clearable />
                    </el-form-item>
                    <el-form-item label="客户手机">
                        <el-input v-model="searchForm.customer_phone" placeholder="请输入客户手机号" clearable />
                    </el-form-item>
                    <el-form-item label="门店">
                        <el-select v-model="searchForm.store_id" placeholder="请选择门店" clearable>
                            <el-option 
                                v-for="store in storeOptions" 
                                :key="store.id" 
                                :label="store.store_name" 
                                :value="store.id" 
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="订单状态">
                        <el-select v-model="searchForm.order_status" placeholder="请选择订单状态" clearable>
                            <el-option label="待评估" :value="1" />
                            <el-option label="已评估" :value="2" />
                            <el-option label="已成交" :value="3" />
                            <el-option label="已取消" :value="4" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建时间">
                        <el-date-picker
                            v-model="searchForm.create_time"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="YYYY-MM-DD"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="loadOrderList">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 订单列表 -->
            <el-table
                v-loading="loading"
                :data="orderList.data"
                stripe
                style="width: 100%"
            >
                <el-table-column prop="order_no" label="订单编号" width="180" />
                <el-table-column prop="customer_name" label="客户姓名" width="100" />
                <el-table-column prop="customer_phone" label="客户手机" width="120" />
                <el-table-column prop="phone_model" label="手机型号" min-width="150">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-image
                                :src="row.phone_model?.image ? img(row.phone_model.image) : '/admin/static/images/default-phone.png'"
                                style="width: 30px; height: 30px; margin-right: 8px"
                                fit="cover"
                            />
                            <span>{{ row.phone_model?.name || '-' }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="estimated_price" label="预估价格" width="100" align="right">
                    <template #default="{ row }">
                        <span class="text-orange-500">¥{{ row.estimated_price }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="final_price" label="成交价格" width="100" align="right">
                    <template #default="{ row }">
                        <span v-if="row.final_price > 0" class="text-green-500">¥{{ row.final_price }}</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="order_status" label="订单状态" width="100" align="center">
                    <template #default="{ row }">
                        <el-tag :type="getOrderStatusTagType(row.order_status)">
                            {{ getOrderStatusText(row.order_status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="store_name" label="门店" width="120" />
                <el-table-column prop="staff_name" label="处理员工" width="100" />
                <el-table-column prop="create_time" label="创建时间" width="160" />
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="viewOrder(row)">
                            查看
                        </el-button>
                        <el-button 
                            v-if="row.order_status === 1" 
                            type="warning" 
                            link 
                            @click="evaluateOrder(row)"
                        >
                            评估
                        </el-button>
                        <el-button 
                            v-if="row.order_status === 2" 
                            type="success" 
                            link 
                            @click="completeOrder(row)"
                        >
                            成交
                        </el-button>
                        <el-button 
                            v-if="row.order_status < 3" 
                            type="danger" 
                            link 
                            @click="cancelOrder(row)"
                        >
                            取消
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="flex justify-end mt-4">
                <el-pagination
                    v-model:current-page="orderList.page"
                    v-model:page-size="orderList.limit"
                    :total="orderList.total"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="loadOrderList"
                    @current-change="loadOrderList"
                />
            </div>
        </el-card>

        <!-- 订单详情弹窗 -->
        <el-dialog
            v-model="showDetailDialog"
            title="订单详情"
            width="800px"
            :destroy-on-close="true"
        >
            <order-detail
                v-if="showDetailDialog"
                :order-id="currentOrder?.id"
                @close="showDetailDialog = false"
            />
        </el-dialog>

        <!-- 添加订单弹窗 -->
        <order-add
            v-model="showAddDialog"
            @complete="handleAddComplete"
        />

    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { img } from '@/utils/common'
import { getRecycleOrderList, cancelRecycleOrder } from '@/addon/yy_phone_recycle/api/recycle'
import { getStoreOptions } from '@/addon/yy_phone_recycle/api/store'
import OrderDetail from './components/order-detail.vue'
import OrderAdd from './components/order-add.vue'

const loading = ref(false)
const showDetailDialog = ref(false)
const showAddDialog = ref(false)
const currentOrder = ref(null)
const searchFormRef = ref()
const storeOptions = ref([])

// 搜索表单
const searchForm = reactive({
    order_no: '',
    customer_phone: '',
    store_id: '',
    order_status: '',
    create_time: []
})

// 订单列表数据
const orderList = reactive({
    data: [],
    total: 0,
    page: 1,
    limit: 10
})

/**
 * 加载订单列表
 */
const loadOrderList = async () => {
    loading.value = true
    try {
        const params = {
            ...searchForm,
            page: orderList.page,
            limit: orderList.limit
        }
        const res = await getRecycleOrderList(params)
        orderList.data = res.data.data
        orderList.total = res.data.total
    } catch (error) {
        console.error('加载订单列表失败:', error)
        ElMessage.error('加载订单列表失败')
    } finally {
        loading.value = false
    }
}

/**
 * 加载门店选项
 */
const loadStoreOptions = async () => {
    try {
        const res = await getStoreOptions()
        storeOptions.value = res.data
    } catch (error) {
        console.error('加载门店选项失败:', error)
    }
}

/**
 * 重置搜索
 */
const resetSearch = () => {
    searchFormRef.value?.resetFields()
    orderList.page = 1
    loadOrderList()
}

/**
 * 获取订单状态文本
 */
const getOrderStatusText = (status: number) => {
    const statusMap = {
        1: '待评估',
        2: '已评估',
        3: '已成交',
        4: '已取消'
    }
    return statusMap[status] || '未知'
}

/**
 * 获取订单状态标签类型
 */
const getOrderStatusTagType = (status: number) => {
    const typeMap = {
        1: 'warning',
        2: 'primary',
        3: 'success',
        4: 'danger'
    }
    return typeMap[status] || 'info'
}

/**
 * 查看订单详情
 */
const viewOrder = (order: any) => {
    currentOrder.value = order
    showDetailDialog.value = true
}

/**
 * 评估订单
 */
const evaluateOrder = (order: any) => {
    // TODO: 跳转到评估页面
    ElMessage.info('评估功能开发中')
}

/**
 * 完成订单
 */
const completeOrder = (order: any) => {
    // TODO: 完成订单逻辑
    ElMessage.info('完成订单功能开发中')
}

/**
 * 取消订单
 */
const cancelOrder = (order: any) => {
    ElMessageBox.confirm(
        `确定要取消订单"${order.order_no}"吗？`,
        '取消确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(async () => {
        try {
            await cancelRecycleOrder(order.id)
            loadOrderList()
        } catch (error) {
            console.error('取消订单失败:', error)
        }
    })
}

/**
 * 添加订单
 */
const handleAdd = () => {
    showAddDialog.value = true
}

/**
 * 添加订单完成
 */
const handleAddComplete = () => {
    loadOrderList()
}

onMounted(() => {
    loadStoreOptions()
    loadOrderList()
})
</script>

<style lang="scss" scoped>
.table-search-wrap {
    .el-form {
        .el-form-item {
            margin-bottom: 16px;
        }
    }
}
</style>
