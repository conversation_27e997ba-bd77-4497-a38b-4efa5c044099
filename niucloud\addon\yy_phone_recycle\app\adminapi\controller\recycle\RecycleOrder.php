<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\adminapi\controller\recycle;

use core\base\BaseAdminController;
use addon\yy_phone_recycle\app\service\admin\recycle\RecycleOrderService;
use think\Response;

/**
 * 回收订单控制器
 * Class RecycleOrder
 * @package addon\yy_phone_recycle\app\adminapi\controller\recycle
 */
class RecycleOrder extends BaseAdminController
{
    /**
     * 获取回收订单列表
     * @return Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ["order_no", ""],
            ["customer_phone", ""],
            ["store_id", ""],
            ["order_status", ""],
            ["create_time", []],
        ]);
        return success((new RecycleOrderService())->getPage($data));
    }

    /**
     * 获取回收订单详情
     * @param int $id
     * @return Response
     */
    public function info(int $id)
    {
        return success((new RecycleOrderService())->getInfo($id));
    }

    /**
     * 添加回收订单
     * @return Response
     */
    public function add()
    {
        $data = $this->request->params([
            ["store_id", 0],
            ["phone_model_id", 0],
            ["evaluation_template_id", 0],
            ["customer_name", ""],
            ["customer_phone", ""],
            ["customer_id_card", ""],
            ["phone_imei", ""],
            ["phone_color", ""],
            ["phone_storage", ""],
            ["estimated_price", 0],
            ["payment_method", 1],
            ["payment_account", ""],
            ["remarks", ""],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\recycle\RecycleOrder.add');
        $id = (new RecycleOrderService())->add($data);
        return success('ADD_SUCCESS', ['id' => $id]);
    }

    /**
     * 编辑回收订单
     * @param int $id
     * @return Response
     */
    public function edit(int $id)
    {
        $data = $this->request->params([
            ["store_id", 0],
            ["staff_id", 0],
            ["phone_model_id", 0],
            ["evaluation_record_id", 0],
            ["customer_name", ""],
            ["customer_phone", ""],
            ["customer_id_card", ""],
            ["phone_imei", ""],
            ["phone_color", ""],
            ["phone_storage", ""],
            ["quality_grade", ""],
            ["base_price", 0],
            ["estimated_price", 0],
            ["final_price", 0],
            ["payment_method", 1],
            ["payment_account", ""],
            ["remarks", ""],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\recycle\RecycleOrder.edit');
        (new RecycleOrderService())->edit($id, $data);
        return success('EDIT_SUCCESS');
    }

    /**
     * 删除回收订单
     * @param int $id
     * @return Response
     */
    public function del(int $id)
    {
        (new RecycleOrderService())->del($id);
        return success('DELETE_SUCCESS');
    }

    /**
     * 取消订单
     * @param int $id
     * @return Response
     */
    public function cancel(int $id)
    {
        $data = $this->request->params([
            ["cancel_reason", ""],
        ]);
        (new RecycleOrderService())->cancel($id, $data);
        return success('CANCEL_SUCCESS');
    }

    /**
     * 完成订单
     * @param int $id
     * @return Response
     */
    public function complete(int $id)
    {
        $data = $this->request->params([
            ["final_price", 0],
            ["payment_method", 1],
            ["payment_account", ""],
            ["remarks", ""],
        ]);
        (new RecycleOrderService())->complete($id, $data);
        return success('COMPLETE_SUCCESS');
    }

    /**
     * 修改订单状态
     * @param int $id
     * @return Response
     */
    public function modifyStatus(int $id)
    {
        $data = $this->request->params([
            ["order_status", 1],
        ]);
        (new RecycleOrderService())->modifyStatus($id, $data);
        return success('MODIFY_SUCCESS');
    }
}
