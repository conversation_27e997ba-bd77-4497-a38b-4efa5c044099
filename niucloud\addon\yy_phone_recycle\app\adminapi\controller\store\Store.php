<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\adminapi\controller\store;

use core\base\BaseAdminController;
use addon\yy_phone_recycle\app\service\admin\store\StoreService;
use addon\yy_phone_recycle\app\service\admin\store\StoreStaffService;
use think\Response;

/**
 * 门店控制器
 * Class Store
 * @package addon\yy_phone_recycle\app\adminapi\controller\store
 */
class Store extends BaseAdminController
{
    /**
     * 获取门店列表
     * @return Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ["store_name", ""],
            ["store_type", ""],
            ["status", ""],
            ["city", ""],
        ]);
        return success((new StoreService())->getPage($data));
    }

    /**
     * 获取门店列表（别名方法，兼容不同路由）
     * @return Response
     */
    public function index()
    {
        return $this->lists();
    }

    /**
     * 获取门店详情
     * @param int $id
     * @return Response
     */
    public function info(int $id)
    {
        return success((new StoreService())->getInfo($id));
    }

    /**
     * 获取门店详情（RESTful方法）
     * @param int $id
     * @return Response
     */
    public function read(int $id)
    {
        return $this->info($id);
    }

    /**
     * 添加门店
     * @return Response
     */
    public function add()
    {
        $data = $this->request->params([
            ["store_code", ""],
            ["store_name", ""],
            ["store_type", 1],
            ["manager_id", 0],
            ["contact_name", ""],
            ["contact_phone", ""],
            ["province", ""],
            ["city", ""],
            ["district", ""],
            ["address", ""],
            ["longitude", 0],
            ["latitude", 0],
            ["business_hours", []],
            ["business_license", ""],
            ["store_images", []],
            ["description", ""],
            ["status", 1],
            ["sort", 0],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\store\Store.add');
        $id = (new StoreService())->add($data);
        return success('ADD_SUCCESS', ['id' => $id]);
    }

    /**
     * 编辑门店
     * @param int $id
     * @return Response
     */
    public function edit(int $id)
    {
        $data = $this->request->params([
            ["store_code", ""],
            ["store_name", ""],
            ["store_type", 1],
            ["manager_id", 0],
            ["contact_name", ""],
            ["contact_phone", ""],
            ["province", ""],
            ["city", ""],
            ["district", ""],
            ["address", ""],
            ["longitude", 0],
            ["latitude", 0],
            ["business_hours", []],
            ["business_license", ""],
            ["store_images", []],
            ["description", ""],
            ["status", 1],
            ["sort", 0],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\store\Store.edit');
        (new StoreService())->edit($id, $data);
        return success('EDIT_SUCCESS');
    }

    /**
     * 更新门店（RESTful方法）
     * @param int $id
     * @return Response
     */
    public function update(int $id)
    {
        return $this->edit($id);
    }

    /**
     * 删除门店
     * @param int $id
     * @return Response
     */
    public function del(int $id)
    {
        (new StoreService())->del($id);
        return success('DELETE_SUCCESS');
    }

    /**
     * 删除门店（RESTful方法）
     * @param int $id
     * @return Response
     */
    public function delete(int $id)
    {
        return $this->del($id);
    }

    /**
     * 修改门店状态
     * @param int $id
     * @return Response
     */
    public function modifyStatus(int $id)
    {
        $data = $this->request->params([
            ["status", 1],
        ]);
        (new StoreService())->modifyStatus($id, $data);
        return success('MODIFY_SUCCESS');
    }



    /**
     * 获取门店统计信息
     * @param int $id
     * @return Response
     */
    public function getStatistics(int $id)
    {
        return success((new StoreService())->getStatistics($id));
    }

    /**
     * 获取门店选项列表
     * @return Response
     */
    public function getOptions()
    {
        return success((new StoreService())->getStoreOptions());
    }

    /**
     * 获取门店选项列表（别名）
     * @return Response
     */
    public function options()
    {
        return $this->getOptions();
    }

    /**
     * 获取门店员工列表
     * @param int $id
     * @return Response
     */
    public function staff(int $id)
    {
        $data = $this->request->params([
            ["real_name", ""],
            ["member_id", ""],
            ["status", ""],
        ]);
        $data['store_id'] = $id;
        return success((new StoreStaffService())->getPage($data));
    }

    /**
     * 添加门店员工
     * @param int $id
     * @return Response
     */
    public function addStaff(int $id)
    {
        $data = $this->request->params([
            ["member_id", 0],
            ["staff_code", ""],
            ["real_name", ""],
            ["phone", ""],
            ["id_card", ""],
            ["entry_date", ""],
            ["status", 1],
        ]);
        $data['store_id'] = $id;
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\store\StoreStaff.add');
        $staff_id = (new StoreStaffService())->add($data);
        return success('ADD_SUCCESS', ['id' => $staff_id]);
    }

    /**
     * 编辑门店员工
     * @param int $id
     * @param int $staff_id
     * @return Response
     */
    public function editStaff(int $id, int $staff_id)
    {
        $data = $this->request->params([
            ["member_id", 0],
            ["staff_code", ""],
            ["real_name", ""],
            ["phone", ""],
            ["id_card", ""],
            ["entry_date", ""],
            ["status", 1],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\store\StoreStaff.edit');
        (new StoreStaffService())->edit($staff_id, $data);
        return success('EDIT_SUCCESS');
    }

    /**
     * 删除门店员工
     * @param int $id
     * @param int $staff_id
     * @return Response
     */
    public function delStaff(int $id, int $staff_id)
    {
        (new StoreStaffService())->del($staff_id);
        return success('DELETE_SUCCESS');
    }

    /**
     * 修改员工状态
     * @param int $id
     * @param int $staff_id
     * @return Response
     */
    public function modifyStaffStatus(int $id, int $staff_id)
    {
        $data = $this->request->params([
            ["status", 1],
        ]);
        (new StoreStaffService())->modifyStatus($staff_id, $data);
        return success('MODIFY_SUCCESS');
    }
}
