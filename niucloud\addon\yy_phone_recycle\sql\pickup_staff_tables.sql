-- ==================== 上门回收员管理相关表 ====================

-- 上门回收员表
DROP TABLE IF EXISTS `{{prefix}}yy_phone_recycle_pickup_staff`;
CREATE TABLE `{{prefix}}yy_phone_recycle_pickup_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '回收员ID',
  `staff_code` varchar(50) NOT NULL DEFAULT '' COMMENT '员工编号',
  `real_name` varchar(50) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `id_card` varchar(18) NOT NULL DEFAULT '' COMMENT '身份证号',
  `avatar` varchar(500) NOT NULL DEFAULT '' COMMENT '头像',
  `gender` tinyint(1) NOT NULL DEFAULT 1 COMMENT '性别：1男 2女',
  `age` int(11) NOT NULL DEFAULT 0 COMMENT '年龄',
  `address` varchar(200) NOT NULL DEFAULT '' COMMENT '居住地址',
  `emergency_contact` varchar(50) NOT NULL DEFAULT '' COMMENT '紧急联系人',
  `emergency_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '紧急联系电话',
  `entry_date` date NOT NULL COMMENT '入职日期',
  `work_experience` int(11) NOT NULL DEFAULT 0 COMMENT '工作经验(年)',
  `education` varchar(50) NOT NULL DEFAULT '' COMMENT '学历',
  `service_areas` json NULL COMMENT '服务区域配置',
  `work_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '工作状态：1空闲 2忙碌 3休息 4离线',
  `current_location` varchar(200) NOT NULL DEFAULT '' COMMENT '当前位置',
  `longitude` decimal(10,6) NOT NULL DEFAULT 0.000000 COMMENT '经度',
  `latitude` decimal(10,6) NOT NULL DEFAULT 0.000000 COMMENT '纬度',
  `location_update_time` datetime NULL COMMENT '位置更新时间',
  `rating` decimal(3,2) NOT NULL DEFAULT 5.00 COMMENT '服务评分',
  `total_orders` int(11) NOT NULL DEFAULT 0 COMMENT '总订单数',
  `completed_orders` int(11) NOT NULL DEFAULT 0 COMMENT '完成订单数',
  `cancelled_orders` int(11) NOT NULL DEFAULT 0 COMMENT '取消订单数',
  `total_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总交易金额',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '提成比例(%)',
  `total_commission` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '总提成金额',
  `vehicle_type` varchar(50) NOT NULL DEFAULT '' COMMENT '交通工具类型',
  `vehicle_number` varchar(50) NOT NULL DEFAULT '' COMMENT '车牌号/车辆编号',
  `work_time_start` time NOT NULL DEFAULT '09:00:00' COMMENT '工作开始时间',
  `work_time_end` time NOT NULL DEFAULT '18:00:00' COMMENT '工作结束时间',
  `max_daily_orders` int(11) NOT NULL DEFAULT 10 COMMENT '每日最大接单数',
  `skills` json NULL COMMENT '技能标签',
  `certifications` json NULL COMMENT '认证证书',
  `bank_name` varchar(100) NOT NULL DEFAULT '' COMMENT '银行名称',
  `bank_account` varchar(50) NOT NULL DEFAULT '' COMMENT '银行账号',
  `account_holder` varchar(50) NOT NULL DEFAULT '' COMMENT '开户人姓名',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1在职 2休假 0离职',
  `remarks` text NULL COMMENT '备注信息',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_staff_code` (`staff_code`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_id_card` (`id_card`),
  KEY `idx_work_status` (`work_status`),
  KEY `idx_status` (`status`),
  KEY `idx_rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='上门回收员表';

-- 回收员工作记录表
DROP TABLE IF EXISTS `{{prefix}}yy_phone_recycle_pickup_work_log`;
CREATE TABLE `{{prefix}}yy_phone_recycle_pickup_work_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `staff_id` int(11) NOT NULL COMMENT '回收员ID',
  `work_date` date NOT NULL COMMENT '工作日期',
  `clock_in_time` datetime NULL COMMENT '上班打卡时间',
  `clock_out_time` datetime NULL COMMENT '下班打卡时间',
  `work_hours` decimal(4,2) NOT NULL DEFAULT 0.00 COMMENT '工作时长(小时)',
  `total_orders` int(11) NOT NULL DEFAULT 0 COMMENT '当日订单数',
  `completed_orders` int(11) NOT NULL DEFAULT 0 COMMENT '完成订单数',
  `cancelled_orders` int(11) NOT NULL DEFAULT 0 COMMENT '取消订单数',
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '当日交易金额',
  `commission_amount` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '当日提成金额',
  `travel_distance` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '行驶距离(公里)',
  `fuel_cost` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '油费成本',
  `work_summary` text NULL COMMENT '工作总结',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_staff_date` (`staff_id`, `work_date`),
  KEY `idx_work_date` (`work_date`),
  KEY `idx_staff_id` (`staff_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='回收员工作记录表';

-- 回收员位置轨迹表
DROP TABLE IF EXISTS `{{prefix}}yy_phone_recycle_pickup_location_track`;
CREATE TABLE `{{prefix}}yy_phone_recycle_pickup_location_track` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '轨迹ID',
  `staff_id` int(11) NOT NULL COMMENT '回收员ID',
  `longitude` decimal(10,6) NOT NULL COMMENT '经度',
  `latitude` decimal(10,6) NOT NULL COMMENT '纬度',
  `address` varchar(200) NOT NULL DEFAULT '' COMMENT '地址描述',
  `speed` decimal(6,2) NOT NULL DEFAULT 0.00 COMMENT '速度(km/h)',
  `direction` decimal(6,2) NOT NULL DEFAULT 0.00 COMMENT '方向角度',
  `accuracy` decimal(6,2) NOT NULL DEFAULT 0.00 COMMENT '定位精度(米)',
  `track_time` datetime NOT NULL COMMENT '轨迹时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_staff_id` (`staff_id`),
  KEY `idx_track_time` (`track_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='回收员位置轨迹表';

-- 回收员评价表
DROP TABLE IF EXISTS `{{prefix}}yy_phone_recycle_pickup_review`;
CREATE TABLE `{{prefix}}yy_phone_recycle_pickup_review` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `staff_id` int(11) NOT NULL COMMENT '回收员ID',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `service_rating` tinyint(1) NOT NULL DEFAULT 5 COMMENT '服务评分：1-5分',
  `punctuality_rating` tinyint(1) NOT NULL DEFAULT 5 COMMENT '准时性评分：1-5分',
  `attitude_rating` tinyint(1) NOT NULL DEFAULT 5 COMMENT '态度评分：1-5分',
  `professional_rating` tinyint(1) NOT NULL DEFAULT 5 COMMENT '专业性评分：1-5分',
  `overall_rating` decimal(3,2) NOT NULL DEFAULT 5.00 COMMENT '综合评分',
  `review_content` text NULL COMMENT '评价内容',
  `review_images` json NULL COMMENT '评价图片',
  `review_tags` json NULL COMMENT '评价标签',
  `reply_content` text NULL COMMENT '回复内容',
  `reply_time` datetime NULL COMMENT '回复时间',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否匿名：1匿名 0不匿名',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1正常 0隐藏',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_staff_id` (`staff_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_overall_rating` (`overall_rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='回收员评价表';

-- 回收员培训记录表
DROP TABLE IF EXISTS `{{prefix}}yy_phone_recycle_pickup_training`;
CREATE TABLE `{{prefix}}yy_phone_recycle_pickup_training` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '培训ID',
  `staff_id` int(11) NOT NULL COMMENT '回收员ID',
  `training_title` varchar(100) NOT NULL DEFAULT '' COMMENT '培训标题',
  `training_type` varchar(50) NOT NULL DEFAULT '' COMMENT '培训类型',
  `training_content` text NULL COMMENT '培训内容',
  `training_date` date NOT NULL COMMENT '培训日期',
  `training_hours` decimal(4,2) NOT NULL DEFAULT 0.00 COMMENT '培训时长(小时)',
  `trainer` varchar(50) NOT NULL DEFAULT '' COMMENT '培训师',
  `training_score` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '培训成绩',
  `is_passed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否通过：1通过 0未通过',
  `certificate` varchar(500) NOT NULL DEFAULT '' COMMENT '证书文件',
  `remarks` text NULL COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_staff_id` (`staff_id`),
  KEY `idx_training_date` (`training_date`),
  KEY `idx_is_passed` (`is_passed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='回收员培训记录表';
