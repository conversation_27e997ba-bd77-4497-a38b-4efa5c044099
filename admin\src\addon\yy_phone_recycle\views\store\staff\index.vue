<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <span class="text-page-title">门店员工管理</span>
                <el-button type="primary" @click="handleAdd">
                    <template #icon>
                        <icon name="element-Plus" />
                    </template>
                    添加员工
                </el-button>
            </div>

            <!-- 搜索表单 -->
            <el-card class="box-card !border-none my-[10px] table-search-wrap" shadow="never">
                <el-form :inline="true" :model="searchForm" ref="searchFormRef">
                    <el-form-item label="门店">
                        <el-select v-model="searchForm.store_id" placeholder="请选择门店" clearable>
                            <el-option 
                                v-for="store in storeOptions" 
                                :key="store.id" 
                                :label="store.store_name" 
                                :value="store.id" 
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="员工姓名">
                        <el-input v-model="searchForm.real_name" placeholder="请输入员工姓名" clearable />
                    </el-form-item>
                    <el-form-item label="角色类型">
                        <el-select v-model="searchForm.role_type" placeholder="请选择角色类型" clearable>
                            <el-option label="店长" :value="1" />
                            <el-option label="收银员" :value="2" />
                            <el-option label="评估师" :value="3" />
                            <el-option label="普通员工" :value="4" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                            <el-option label="在职" :value="1" />
                            <el-option label="离职" :value="0" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="loadStaffList">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 员工列表 -->
            <div class="mt-4">
                <el-table :data="staffList" size="large" v-loading="loading">
                    <el-table-column prop="staff_code" label="员工编号" min-width="120" />
                    <el-table-column prop="real_name" label="姓名" min-width="100" />
                    <el-table-column prop="phone" label="手机号" min-width="120" />
                    <el-table-column prop="store.store_name" label="所属门店" min-width="120">
                        <template #default="{ row }">
                            {{ row.store?.store_name || '未分配' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="position" label="职位" min-width="100" />
                    <el-table-column prop="role_type" label="角色类型" min-width="100">
                        <template #default="{ row }">
                            <el-tag :type="getRoleTypeTagType(row.role_type)">
                                {{ getRoleTypeName(row.role_type) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="entry_date" label="入职日期" min-width="120" />
                    <el-table-column prop="salary_type" label="薪资类型" min-width="100">
                        <template #default="{ row }">
                            {{ getSalaryTypeName(row.salary_type) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="base_salary" label="基础工资" min-width="100">
                        <template #default="{ row }">
                            ¥{{ row.base_salary }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" min-width="80">
                        <template #default="{ row }">
                            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                                {{ row.status === 1 ? '在职' : '离职' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" fixed="right" min-width="200">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="viewStaff(row)">查看</el-button>
                            <el-button type="primary" link @click="editStaff(row)">编辑</el-button>
                            <el-button 
                                :type="row.status === 1 ? 'warning' : 'success'" 
                                link 
                                @click="toggleStatus(row)"
                            >
                                {{ row.status === 1 ? '离职' : '复职' }}
                            </el-button>
                            <el-button type="danger" link @click="deleteStaff(row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="flex justify-end mt-4">
                    <el-pagination
                        v-model:current-page="pageData.page"
                        v-model:page-size="pageData.limit"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pageData.total"
                        @size-change="loadStaffList"
                        @current-change="loadStaffList"
                    />
                </div>
            </div>
        </el-card>

        <!-- 员工详情弹窗 -->
        <el-dialog
            v-model="showDetailDialog"
            title="员工详情"
            width="800px"
            :destroy-on-close="true"
        >
            <staff-detail
                v-if="showDetailDialog"
                :staff-id="currentStaff?.id"
                @close="showDetailDialog = false"
            />
        </el-dialog>

        <!-- 添加/编辑员工弹窗 -->
        <staff-add-edit 
            v-model="showAddEditDialog" 
            :staff-data="currentStaff"
            :is-edit="isEdit"
            @complete="handleAddEditComplete"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getStoreStaffList, deleteStoreStaff, modifyStoreStaffStatus, getStoreOptions } from '@/addon/yy_phone_recycle/api/store'
import StaffDetail from './components/staff-detail.vue'
import StaffAddEdit from './components/staff-add-edit.vue'

const loading = ref(false)
const showDetailDialog = ref(false)
const showAddEditDialog = ref(false)
const isEdit = ref(false)
const currentStaff = ref(null)
const searchFormRef = ref()
const storeOptions = ref([])

// 搜索表单
const searchForm = reactive({
    store_id: '',
    real_name: '',
    role_type: '',
    status: ''
})

// 分页数据
const pageData = reactive({
    page: 1,
    limit: 10,
    total: 0
})

// 员工列表
const staffList = ref([])

/**
 * 加载门店选项
 */
const loadStoreOptions = async () => {
    try {
        const { data } = await getStoreOptions()
        storeOptions.value = data
    } catch (error) {
        console.error('加载门店选项失败:', error)
    }
}

/**
 * 加载员工列表
 */
const loadStaffList = async () => {
    loading.value = true
    try {
        const params = {
            ...searchForm,
            page: pageData.page,
            limit: pageData.limit
        }
        const { data } = await getStoreStaffList(params)
        staffList.value = data.data
        pageData.total = data.total
    } catch (error) {
        console.error('加载员工列表失败:', error)
        ElMessage.error('加载员工列表失败')
    } finally {
        loading.value = false
    }
}

/**
 * 重置搜索
 */
const resetSearch = () => {
    searchFormRef.value?.resetFields()
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
    })
    pageData.page = 1
    loadStaffList()
}

/**
 * 获取角色类型名称
 */
const getRoleTypeName = (roleType: number) => {
    const roleMap = {
        1: '店长',
        2: '收银员',
        3: '评估师',
        4: '普通员工'
    }
    return roleMap[roleType] || '未知'
}

/**
 * 获取角色类型标签类型
 */
const getRoleTypeTagType = (roleType: number) => {
    const typeMap = {
        1: 'danger',
        2: 'warning',
        3: 'success',
        4: 'info'
    }
    return typeMap[roleType] || 'info'
}

/**
 * 获取薪资类型名称
 */
const getSalaryTypeName = (salaryType: number) => {
    const salaryMap = {
        1: '固定工资',
        2: '提成制',
        3: '混合制'
    }
    return salaryMap[salaryType] || '未知'
}

/**
 * 查看员工详情
 */
const viewStaff = (staff: any) => {
    currentStaff.value = staff
    showDetailDialog.value = true
}

/**
 * 添加员工
 */
const handleAdd = () => {
    currentStaff.value = null
    isEdit.value = false
    showAddEditDialog.value = true
}

/**
 * 编辑员工
 */
const editStaff = (staff: any) => {
    currentStaff.value = staff
    isEdit.value = true
    showAddEditDialog.value = true
}

/**
 * 添加/编辑完成
 */
const handleAddEditComplete = () => {
    loadStaffList()
}

/**
 * 切换员工状态
 */
const toggleStatus = (staff: any) => {
    const action = staff.status === 1 ? '离职' : '复职'
    const newStatus = staff.status === 1 ? 0 : 1
    
    ElMessageBox.confirm(
        `确定要将员工"${staff.real_name}"设置为${action}状态吗？`,
        '状态确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(async () => {
        try {
            await modifyStoreStaffStatus(staff.id, { status: newStatus })
            ElMessage.success(`${action}成功`)
            loadStaffList()
        } catch (error) {
            console.error(`${action}失败:`, error)
        }
    })
}

/**
 * 删除员工
 */
const deleteStaff = (staff: any) => {
    ElMessageBox.confirm(
        `确定要删除员工"${staff.real_name}"吗？此操作不可恢复！`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(async () => {
        try {
            await deleteStoreStaff(staff.id)
            ElMessage.success('删除成功')
            loadStaffList()
        } catch (error) {
            console.error('删除员工失败:', error)
        }
    })
}

onMounted(() => {
    loadStoreOptions()
    loadStaffList()
})
</script>

<style lang="scss" scoped>
.table-search-wrap {
    .el-form {
        .el-form-item {
            margin-bottom: 16px;
        }
    }
}
</style>
