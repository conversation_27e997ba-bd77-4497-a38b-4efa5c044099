<?php
/**
 * 修复门店图片数据脚本
 * 将无效的JSON数据转换为有效的JSON格式
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use think\facade\Db;

try {
    // 获取所有门店数据
    $stores = Db::name('yy_phone_recycle_store')->select();
    
    foreach ($stores as $store) {
        $needUpdate = false;
        $updateData = [];
        
        // 修复store_images字段
        if (!empty($store['store_images'])) {
            // 尝试解析JSON
            $images = json_decode($store['store_images'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                // JSON解析失败，尝试其他方式处理
                $imageStr = trim($store['store_images'], '"\'[]');
                if (!empty($imageStr)) {
                    // 按逗号分割
                    $imageArray = array_filter(explode(',', $imageStr));
                    $imageArray = array_map('trim', $imageArray);
                    $updateData['store_images'] = json_encode($imageArray);
                } else {
                    $updateData['store_images'] = json_encode([]);
                }
                $needUpdate = true;
                echo "修复门店 {$store['id']} 的store_images字段\n";
            }
        }
        
        // 修复business_hours字段
        if (!empty($store['business_hours'])) {
            $hours = json_decode($store['business_hours'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                // 设置默认营业时间
                $defaultHours = [
                    'monday' => ['enabled' => true, 'start' => '09:00', 'end' => '18:00'],
                    'tuesday' => ['enabled' => true, 'start' => '09:00', 'end' => '18:00'],
                    'wednesday' => ['enabled' => true, 'start' => '09:00', 'end' => '18:00'],
                    'thursday' => ['enabled' => true, 'start' => '09:00', 'end' => '18:00'],
                    'friday' => ['enabled' => true, 'start' => '09:00', 'end' => '18:00'],
                    'saturday' => ['enabled' => true, 'start' => '09:00', 'end' => '18:00'],
                    'sunday' => ['enabled' => false, 'start' => '09:00', 'end' => '18:00']
                ];
                $updateData['business_hours'] = json_encode($defaultHours);
                $needUpdate = true;
                echo "修复门店 {$store['id']} 的business_hours字段\n";
            }
        }
        
        // 执行更新
        if ($needUpdate) {
            Db::name('yy_phone_recycle_store')
                ->where('id', $store['id'])
                ->update($updateData);
            echo "门店 {$store['id']} 数据修复完成\n";
        }
    }
    
    echo "所有门店数据修复完成！\n";
    
} catch (Exception $e) {
    echo "修复失败: " . $e->getMessage() . "\n";
}
