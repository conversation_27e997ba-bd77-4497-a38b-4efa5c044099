<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\service\admin\store;

use core\base\BaseService;
use think\facade\Db;

/**
 * 地区服务类
 */
class AreaService extends BaseService
{
    /**
     * 根据地区名称获取ID
     * @param string $provinceName
     * @param string $cityName
     * @param string $districtName
     * @return array
     */
    public function getAreaIdsByName(string $provinceName, string $cityName, string $districtName): array
    {
        $result = [
            'province_id' => 0,
            'city_id' => 0,
            'district_id' => 0
        ];

        if (empty($provinceName)) {
            return $result;
        }

        // 查询省份ID
        $province = Db::name('sys_area')
            ->where('name', $provinceName)
            ->where('pid', 0)
            ->find();

        if ($province) {
            $result['province_id'] = $province['id'];

            // 查询城市ID
            if (!empty($cityName)) {
                $city = Db::name('sys_area')
                    ->where('name', $cityName)
                    ->where('pid', $province['id'])
                    ->find();

                if ($city) {
                    $result['city_id'] = $city['id'];

                    // 查询区县ID
                    if (!empty($districtName)) {
                        $district = Db::name('sys_area')
                            ->where('name', $districtName)
                            ->where('pid', $city['id'])
                            ->find();

                        if ($district) {
                            $result['district_id'] = $district['id'];
                        }
                    }
                }
            }
        }

        return $result;
    }

    /**
     * 根据ID获取地区名称
     * @param int $provinceId
     * @param int $cityId
     * @param int $districtId
     * @return array
     */
    public function getAreaNamesByIds(int $provinceId, int $cityId, int $districtId): array
    {
        $result = [
            'province' => '',
            'city' => '',
            'district' => ''
        ];

        if ($provinceId > 0) {
            $province = Db::name('sys_area')->where('id', $provinceId)->find();
            if ($province) {
                $result['province'] = $province['name'];
            }
        }

        if ($cityId > 0) {
            $city = Db::name('sys_area')->where('id', $cityId)->find();
            if ($city) {
                $result['city'] = $city['name'];
            }
        }

        if ($districtId > 0) {
            $district = Db::name('sys_area')->where('id', $districtId)->find();
            if ($district) {
                $result['district'] = $district['name'];
            }
        }

        return $result;
    }
}
