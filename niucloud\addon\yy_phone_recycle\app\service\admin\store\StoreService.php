<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\service\admin\store;

use addon\yy_phone_recycle\app\model\store\Store;
use addon\yy_phone_recycle\app\model\store\StoreStaff;
use addon\yy_phone_recycle\app\model\store\StoreEquipment;
use core\base\BaseAdminService;
use core\exception\AdminException;
use app\service\admin\sys\AreaService;

/**
 * 门店服务层
 * Class StoreService
 * @package addon\yy_phone_recycle\app\service\admin\store
 */
class StoreService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new Store();
    }

    /**
     * 获取门店分页列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id,store_code,store_name,store_type,manager_id,contact_name,contact_phone,province_id,city_id,district_id,province,city,district,address,full_address,longitude,latitude,business_hours,business_license,store_images,description,status,sort,create_time,update_time';
        $order = 'sort desc, id desc';

        $search_model = $this->model
            ->withSearch(["store_name", "store_type", "status", "city"], $where)
            ->with(['manager' => function($query) {
                $query->field('uid,username,real_name');
            }])
            ->field($field)
            ->order($order);

        $list = $this->pageQuery($search_model);

        // 处理数据，添加统计信息
        if (!empty($list['data'])) {
            foreach ($list['data'] as &$item) {
                // 统计员工数量
                $staff_count = (new StoreStaff())
                    ->where('store_id', $item['id'])
                    ->where('status', 1)
                    ->count();
                $item['staff_count'] = $staff_count;

                // 统计设备数量
                $equipment_count = (new StoreEquipment())
                    ->where('store_id', $item['id'])
                    ->count();
                $item['equipment_count'] = $equipment_count;

                // 处理图片数据
                if (is_string($item['store_images'])) {
                    $item['store_images'] = json_decode($item['store_images'], true) ?: [];
                }

                // 处理营业时间数据
                if (is_string($item['business_hours'])) {
                    $item['business_hours'] = json_decode($item['business_hours'], true) ?: [];
                }
            }
        }

        return $list;
    }

    /**
     * 获取门店信息
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = 'id,store_code,store_name,store_type,manager_id,contact_name,contact_phone,province_id,city_id,district_id,province,city,district,address,full_address,longitude,latitude,business_hours,business_license,store_images,description,status,sort,create_time,update_time';

        $info = $this->model->field($field)
            ->with(['manager' => function($query) {
                $query->field('uid,username,real_name');
            }])
            ->where([['id', '=', $id]])
            ->findOrEmpty()->toArray();

        if (empty($info)) {
            throw new AdminException('STORE_NOT_EXIST');
        }

        // 处理图片数据
        if (is_string($info['store_images'])) {
            $info['store_images'] = json_decode($info['store_images'], true) ?: [];
        }

        // 处理营业时间数据
        if (is_string($info['business_hours'])) {
            $info['business_hours'] = json_decode($info['business_hours'], true) ?: [];
        }

        return $info;
    }

    /**
     * 添加门店
     * @param array $data
     * @return mixed
     */
    public function add(array $data)
    {
        // 处理地址信息
        $this->handleAddressInfo($data);

        // 处理JSON字段
        if (isset($data['business_hours']) && is_array($data['business_hours'])) {
            $data['business_hours'] = json_encode($data['business_hours']);
        }
        if (isset($data['store_images']) && is_array($data['store_images'])) {
            $data['store_images'] = json_encode($data['store_images']);
        }

        // 生成门店编码
        if (empty($data['store_code'])) {
            $data['store_code'] = 'ST' . date('YmdHis') . rand(1000, 9999);
        }

        $res = $this->model->create($data);
        return $res->id;
    }

    /**
     * 编辑门店
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function edit(int $id, array $data)
    {
        $info = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($info->isEmpty()) {
            throw new AdminException('STORE_NOT_EXIST');
        }

        // 处理地址信息
        $this->handleAddressInfo($data);

        // 处理JSON字段
        if (isset($data['business_hours']) && is_array($data['business_hours'])) {
            $data['business_hours'] = json_encode($data['business_hours']);
        }
        if (isset($data['store_images']) && is_array($data['store_images'])) {
            $data['store_images'] = json_encode($data['store_images']);
        }

        $res = $info->save($data);
        return $res !== false;
    }

    /**
     * 删除门店
     * @param int $id
     * @return bool
     */
    public function del(int $id)
    {
        $info = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($info->isEmpty()) {
            throw new AdminException('STORE_NOT_EXIST');
        }

        // 检查是否有关联数据
        $staff_count = (new StoreStaff())->where('store_id', $id)->count();
        if ($staff_count > 0) {
            throw new AdminException('STORE_HAS_STAFF_CANNOT_DELETE');
        }

        $res = $info->delete();
        return $res !== false;
    }

    /**
     * 修改门店状态
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function modifyStatus(int $id, array $data)
    {
        $info = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($info->isEmpty()) {
            throw new AdminException('STORE_NOT_EXIST');
        }

        $res = $info->save(['status' => $data['status']]);
        return $res !== false;
    }

    /**
     * 获取门店员工分页列表
     * @param array $where
     * @return array
     */
    public function getStaffPage(array $where = [])
    {
        $field = 'id,store_id,user_id,staff_code,real_name,phone,id_card,position,role_type,permissions,entry_date,salary_type,base_salary,commission_rate,status,create_time,update_time';
        $order = 'role_type asc, id desc';

        $search_model = (new StoreStaff())
            ->withSearch(["store_id", "real_name", "role_type", "status"], $where)
            ->with(['user' => function($query) {
                $query->field('uid,username,real_name');
            }])
            ->field($field)
            ->order($order);

        return $this->pageQuery($search_model);
    }

    /**
     * 获取门店设备分页列表
     * @param array $where
     * @return array
     */
    public function getEquipmentPage(array $where = [])
    {
        $field = 'id,store_id,equipment_code,equipment_name,equipment_type,brand,model,serial_number,purchase_date,purchase_price,warranty_period,maintenance_cycle,last_maintenance,next_maintenance,equipment_status,responsible_staff_id,location,specifications,maintenance_records,remarks,create_time,update_time';
        $order = 'equipment_status asc, id desc';

        $search_model = (new StoreEquipment())
            ->withSearch(["store_id", "equipment_name", "equipment_type", "equipment_status"], $where)
            ->with(['responsibleStaff' => function($query) {
                $query->field('id,real_name,phone');
            }])
            ->field($field)
            ->order($order);

        return $this->pageQuery($search_model);
    }

    /**
     * 获取门店统计信息
     * @param int $store_id
     * @return array
     */
    public function getStatistics(int $store_id)
    {
        // 员工统计
        $staff_total = (new StoreStaff())->where('store_id', $store_id)->count();
        $staff_active = (new StoreStaff())->where('store_id', $store_id)->where('status', 1)->count();

        // 设备统计
        $equipment_total = (new StoreEquipment())->where('store_id', $store_id)->count();
        $equipment_normal = (new StoreEquipment())->where('store_id', $store_id)->where('equipment_status', 1)->count();
        $equipment_fault = (new StoreEquipment())->where('store_id', $store_id)->where('equipment_status', 2)->count();

        return [
            'staff' => [
                'total' => $staff_total,
                'active' => $staff_active,
                'inactive' => $staff_total - $staff_active
            ],
            'equipment' => [
                'total' => $equipment_total,
                'normal' => $equipment_normal,
                'fault' => $equipment_fault,
                'maintenance' => (new StoreEquipment())->where('store_id', $store_id)->where('equipment_status', 3)->count()
            ]
        ];
    }

    /**
     * 获取门店选项列表
     * @return array
     */
    public function getStoreOptions()
    {
        return $this->model
            ->where('status', 1)
            ->field('id,store_name,store_code')
            ->order('sort desc, id desc')
            ->select()
            ->toArray();
    }

    /**
     * 处理地址信息
     * @param array $data
     */
    private function handleAddressInfo(array &$data)
    {
        // 如果有省市区ID，获取对应的名称
        if (!empty($data['province_id']) || !empty($data['city_id']) || !empty($data['district_id'])) {
            $areaService = new AreaService();

            if (!empty($data['province_id'])) {
                try {
                    $provinceInfo = $areaService->getInfo($data['province_id']);
                    $data['province'] = $provinceInfo['name'] ?? '';
                } catch (\Exception $e) {
                    $data['province'] = '';
                }
            }

            if (!empty($data['city_id'])) {
                try {
                    $cityInfo = $areaService->getInfo($data['city_id']);
                    $data['city'] = $cityInfo['name'] ?? '';
                } catch (\Exception $e) {
                    $data['city'] = '';
                }
            }

            if (!empty($data['district_id'])) {
                try {
                    $districtInfo = $areaService->getInfo($data['district_id']);
                    $data['district'] = $districtInfo['name'] ?? '';
                } catch (\Exception $e) {
                    $data['district'] = '';
                }
            }

            // 拼接完整地址
            $data['full_address'] = ($data['province'] ?? '') . ($data['city'] ?? '') . ($data['district'] ?? '') . ($data['address'] ?? '');
        }

        // 如果有完整地址，尝试解析经纬度
        if (!empty($data['full_address']) && (empty($data['longitude']) || empty($data['latitude']))) {
            try {
                $areaService = new AreaService();
                $analysis_res = $areaService->getAddress($data['full_address']);
                if ($analysis_res['status'] == 0 && $analysis_res['message'] == 'Success' && !empty($analysis_res['result'])) {
                    $data['longitude'] = $analysis_res['result']['location']['lng'];
                    $data['latitude'] = $analysis_res['result']['location']['lat'];
                }
            } catch (\Exception $e) {
                // 地址解析失败，不影响门店创建
            }
        }
    }
}
