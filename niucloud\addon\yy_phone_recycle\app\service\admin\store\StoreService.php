<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\service\admin\store;

use addon\yy_phone_recycle\app\model\store\Store;
use addon\yy_phone_recycle\app\model\store\StoreStaff;
use core\base\BaseAdminService;
use core\exception\AdminException;

/**
 * 门店服务层
 * Class StoreService
 * @package addon\yy_phone_recycle\app\service\admin\store
 */
class StoreService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new Store();
    }

    /**
     * 获取门店分页列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id,store_code,store_name,store_type,manager_id,contact_name,contact_phone,province,city,district,address,longitude,latitude,business_hours,business_license,store_images,description,status,sort,create_time,update_time';
        $order = 'sort desc, id desc';

        $search_model = $this->model
            ->withSearch(["store_name", "store_type", "status", "city"], $where)
            ->with(['manager' => function($query) {
                $query->field('uid,username,real_name');
            }])
            ->field($field)
            ->order($order);

        $list = $this->pageQuery($search_model);

        // 处理数据，添加统计信息
        if (!empty($list['data'])) {
            foreach ($list['data'] as &$item) {
                // 统计员工数量
                $staff_count = (new StoreStaff())
                    ->where('store_id', $item['id'])
                    ->where('status', 1)
                    ->count();
                $item['staff_count'] = $staff_count;



                // 处理图片数据
                if (is_string($item['store_images'])) {
                    $item['store_images'] = json_decode($item['store_images'], true) ?: [];
                }

                // 处理营业时间数据
                if (is_string($item['business_hours'])) {
                    $item['business_hours'] = json_decode($item['business_hours'], true) ?: [];
                }
            }
        }

        return $list;
    }

    /**
     * 获取门店信息
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = 'id,store_code,store_name,store_type,manager_id,contact_name,contact_phone,province_id,city_id,district_id,province,city,district,address,longitude,latitude,business_hours,business_license,store_images,description,status,sort,create_time,update_time';

        $info = $this->model->field($field)
            ->with(['manager' => function($query) {
                $query->field('uid,username,real_name');
            }])
            ->where([['id', '=', $id]])
            ->findOrEmpty()->toArray();

        if (empty($info)) {
            throw new AdminException('STORE_NOT_EXIST');
        }

        // 调试：记录原始数据
        \think\facade\Log::info('门店原始数据', [
            'store_images_raw' => $info['store_images'] ?? 'not_set',
            'store_images_type' => gettype($info['store_images'] ?? null),
            'business_hours_raw' => $info['business_hours'] ?? 'not_set',
            'business_hours_type' => gettype($info['business_hours'] ?? null)
        ]);

        // 处理图片数据 - 确保返回正确的数组格式
        if (isset($info['store_images'])) {
            if (is_string($info['store_images'])) {
                // 如果是字符串，尝试解析JSON
                $decoded = json_decode($info['store_images'], true);
                $info['store_images'] = is_array($decoded) ? $decoded : [];
            } elseif (is_array($info['store_images'])) {
                // 如果已经是数组，直接使用
                $info['store_images'] = $info['store_images'];
            } else {
                $info['store_images'] = [];
            }
        } else {
            $info['store_images'] = [];
        }

        // 处理营业时间数据
        if (isset($info['business_hours'])) {
            if (is_string($info['business_hours'])) {
                $decoded = json_decode($info['business_hours'], true);
                $info['business_hours'] = is_array($decoded) ? $decoded : [];
            } elseif (is_array($info['business_hours'])) {
                $info['business_hours'] = $info['business_hours'];
            } else {
                $info['business_hours'] = [];
            }
        } else {
            $info['business_hours'] = [];
        }

        // 调试：记录处理后的数据
        \think\facade\Log::info('门店处理后数据', [
            'store_images_processed' => $info['store_images'],
            'business_hours_processed' => $info['business_hours']
        ]);

        // 如果省市区ID为空，尝试根据名称查询ID
        if (($info['province_id'] == 0 || $info['city_id'] == 0 || $info['district_id'] == 0)
            && !empty($info['province']) && !empty($info['city']) && !empty($info['district'])) {

            $areaService = new \addon\yy_phone_recycle\app\service\admin\store\AreaService();
            $areaIds = $areaService->getAreaIdsByName($info['province'], $info['city'], $info['district']);

            $info['province_id'] = $areaIds['province_id'];
            $info['city_id'] = $areaIds['city_id'];
            $info['district_id'] = $areaIds['district_id'];
        }

        return $info;
    }

    /**
     * 添加门店
     * @param array $data
     * @return mixed
     */
    public function add(array $data)
    {
        // 处理JSON字段
        if (isset($data['business_hours']) && is_array($data['business_hours'])) {
            $data['business_hours'] = json_encode($data['business_hours']);
        }

        // 处理门店图片字段
        if (isset($data['store_images'])) {
            if (is_string($data['store_images'])) {
                // 如果是字符串，按逗号分割转为数组
                $images = array_filter(explode(',', $data['store_images']));
                $data['store_images'] = json_encode($images);
            } elseif (is_array($data['store_images'])) {
                $data['store_images'] = json_encode($data['store_images']);
            } else {
                $data['store_images'] = json_encode([]);
            }
        }

        // 处理营业执照字段
        if (isset($data['business_license']) && is_string($data['business_license'])) {
            // 营业执照保持字符串格式
            $data['business_license'] = trim($data['business_license']);
        }

        // 处理省市区ID和名称的同步
        $this->syncAreaData($data);

        // 生成门店编码
        if (empty($data['store_code'])) {
            $data['store_code'] = 'ST' . date('YmdHis') . rand(1000, 9999);
        }

        $res = $this->model->create($data);
        return $res->id;
    }

    /**
     * 编辑门店
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function edit(int $id, array $data)
    {
        $info = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($info->isEmpty()) {
            throw new AdminException('STORE_NOT_EXIST');
        }

        // 处理JSON字段
        if (isset($data['business_hours']) && is_array($data['business_hours'])) {
            $data['business_hours'] = json_encode($data['business_hours']);
        }

        // 处理门店图片字段
        if (isset($data['store_images'])) {
            if (is_string($data['store_images'])) {
                // 如果是字符串，按逗号分割转为数组
                $images = array_filter(explode(',', $data['store_images']));
                $data['store_images'] = json_encode($images);
            } elseif (is_array($data['store_images'])) {
                $data['store_images'] = json_encode($data['store_images']);
            } else {
                $data['store_images'] = json_encode([]);
            }
        }

        // 处理营业执照字段
        if (isset($data['business_license']) && is_string($data['business_license'])) {
            // 营业执照保持字符串格式
            $data['business_license'] = trim($data['business_license']);
        }

        // 处理省市区ID和名称的同步
        $this->syncAreaData($data);

        $res = $info->save($data);
        return $res !== false;
    }

    /**
     * 同步省市区ID和名称数据
     * @param array $data
     */
    private function syncAreaData(array &$data)
    {
        $areaService = new \addon\yy_phone_recycle\app\service\admin\store\AreaService();

        // 如果有省市区ID，但没有名称，根据ID查询名称
        if (isset($data['province_id']) && $data['province_id'] > 0 &&
            isset($data['city_id']) && $data['city_id'] > 0 &&
            isset($data['district_id']) && $data['district_id'] > 0) {

            if (empty($data['province']) || empty($data['city']) || empty($data['district'])) {
                $areaNames = $areaService->getAreaNamesByIds(
                    $data['province_id'],
                    $data['city_id'],
                    $data['district_id']
                );

                $data['province'] = $areaNames['province'] ?: ($data['province'] ?? '');
                $data['city'] = $areaNames['city'] ?: ($data['city'] ?? '');
                $data['district'] = $areaNames['district'] ?: ($data['district'] ?? '');
            }
        }
        // 如果有省市区名称，但没有ID，根据名称查询ID
        elseif (!empty($data['province']) && !empty($data['city']) && !empty($data['district'])) {
            if (empty($data['province_id']) || empty($data['city_id']) || empty($data['district_id'])) {
                $areaIds = $areaService->getAreaIdsByName(
                    $data['province'],
                    $data['city'],
                    $data['district']
                );

                $data['province_id'] = $areaIds['province_id'] ?: ($data['province_id'] ?? 0);
                $data['city_id'] = $areaIds['city_id'] ?: ($data['city_id'] ?? 0);
                $data['district_id'] = $areaIds['district_id'] ?: ($data['district_id'] ?? 0);
            }
        }
    }

    /**
     * 删除门店
     * @param int $id
     * @return bool
     */
    public function del(int $id)
    {
        $info = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($info->isEmpty()) {
            throw new AdminException('STORE_NOT_EXIST');
        }

        // 检查是否有关联数据
        $staff_count = (new StoreStaff())->where('store_id', $id)->count();
        if ($staff_count > 0) {
            throw new AdminException('STORE_HAS_STAFF_CANNOT_DELETE');
        }

        $res = $info->delete();
        return $res !== false;
    }

    /**
     * 修改门店状态
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function modifyStatus(int $id, array $data)
    {
        $info = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($info->isEmpty()) {
            throw new AdminException('STORE_NOT_EXIST');
        }

        $res = $info->save(['status' => $data['status']]);
        return $res !== false;
    }

    /**
     * 获取门店员工分页列表
     * @param array $where
     * @return array
     */
    public function getStaffPage(array $where = [])
    {
        $field = 'id,store_id,user_id,staff_code,real_name,phone,id_card,position,role_type,permissions,entry_date,salary_type,base_salary,commission_rate,status,create_time,update_time';
        $order = 'role_type asc, id desc';

        $search_model = (new StoreStaff())
            ->withSearch(["store_id", "real_name", "role_type", "status"], $where)
            ->with(['user' => function($query) {
                $query->field('uid,username,real_name');
            }])
            ->field($field)
            ->order($order);

        return $this->pageQuery($search_model);
    }

    /**
     * 获取门店设备分页列表
     * @param array $where
     * @return array
     */
    public function getEquipmentPage(array $where = [])
    {
        $field = 'id,store_id,equipment_code,equipment_name,equipment_type,brand,model,serial_number,purchase_date,purchase_price,warranty_period,maintenance_cycle,last_maintenance,next_maintenance,equipment_status,responsible_staff_id,location,specifications,maintenance_records,remarks,create_time,update_time';
        $order = 'equipment_status asc, id desc';

        $search_model = (new StoreEquipment())
            ->withSearch(["store_id", "equipment_name", "equipment_type", "equipment_status"], $where)
            ->with(['responsibleStaff' => function($query) {
                $query->field('id,real_name,phone');
            }])
            ->field($field)
            ->order($order);

        return $this->pageQuery($search_model);
    }

    /**
     * 获取门店统计信息
     * @param int $store_id
     * @return array
     */
    public function getStatistics(int $store_id)
    {
        // 员工统计
        $staff_total = (new StoreStaff())->where('store_id', $store_id)->count();
        $staff_active = (new StoreStaff())->where('store_id', $store_id)->where('status', 1)->count();

        // 设备统计
        $equipment_total = (new StoreEquipment())->where('store_id', $store_id)->count();
        $equipment_normal = (new StoreEquipment())->where('store_id', $store_id)->where('equipment_status', 1)->count();
        $equipment_fault = (new StoreEquipment())->where('store_id', $store_id)->where('equipment_status', 2)->count();

        return [
            'staff' => [
                'total' => $staff_total,
                'active' => $staff_active,
                'inactive' => $staff_total - $staff_active
            ],
            'equipment' => [
                'total' => $equipment_total,
                'normal' => $equipment_normal,
                'fault' => $equipment_fault,
                'maintenance' => (new StoreEquipment())->where('store_id', $store_id)->where('equipment_status', 3)->count()
            ]
        ];
    }

    /**
     * 获取门店选项列表
     * @return array
     */
    public function getStoreOptions()
    {
        return $this->model
            ->where('status', 1)
            ->field('id,store_name,store_code')
            ->order('sort desc, id desc')
            ->select()
            ->toArray();
    }
}
