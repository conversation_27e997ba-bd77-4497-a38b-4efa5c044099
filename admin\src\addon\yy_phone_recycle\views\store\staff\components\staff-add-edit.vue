<template>
    <el-dialog
        v-model="showDialog"
        :title="isEdit ? '编辑员工' : '添加员工'"
        width="800px"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            v-loading="loading"
        >
            <!-- 基本信息 -->
            <div class="form-section">
                <h4 class="section-title">基本信息</h4>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="员工编号" prop="staff_code">
                            <el-input v-model="formData.staff_code" placeholder="请输入员工编号" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="姓名" prop="real_name">
                            <el-input v-model="formData.real_name" placeholder="请输入员工姓名" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="手机号" prop="phone">
                            <el-input v-model="formData.phone" placeholder="请输入手机号" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="身份证号" prop="id_card">
                            <el-input v-model="formData.id_card" placeholder="请输入身份证号（可选）" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 工作信息 -->
            <div class="form-section">
                <h4 class="section-title">工作信息</h4>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="所属门店" prop="store_id">
                            <el-select v-model="formData.store_id" placeholder="请选择门店" class="w-full">
                                <el-option
                                    v-for="store in storeOptions"
                                    :key="store.id"
                                    :label="store.store_name"
                                    :value="store.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="职位" prop="position">
                            <el-input v-model="formData.position" placeholder="请输入职位" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="角色类型" prop="role_type">
                            <el-select v-model="formData.role_type" placeholder="请选择角色类型" class="w-full">
                                <el-option label="店长" :value="1" />
                                <el-option label="收银员" :value="2" />
                                <el-option label="评估师" :value="3" />
                                <el-option label="普通员工" :value="4" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="入职日期" prop="entry_date">
                            <el-date-picker
                                v-model="formData.entry_date"
                                type="date"
                                placeholder="请选择入职日期"
                                value-format="YYYY-MM-DD"
                                class="w-full"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 薪资信息 -->
            <div class="form-section">
                <h4 class="section-title">薪资信息</h4>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="薪资类型" prop="salary_type">
                            <el-select v-model="formData.salary_type" placeholder="请选择薪资类型" class="w-full">
                                <el-option label="固定工资" :value="1" />
                                <el-option label="提成制" :value="2" />
                                <el-option label="混合制" :value="3" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="基础工资" prop="base_salary">
                            <el-input-number
                                v-model="formData.base_salary"
                                :min="0"
                                :precision="2"
                                placeholder="请输入基础工资"
                                class="w-full"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="formData.salary_type !== 1">
                    <el-col :span="12">
                        <el-form-item label="提成比例" prop="commission_rate">
                            <el-input-number
                                v-model="formData.commission_rate"
                                :min="0"
                                :max="100"
                                :precision="2"
                                placeholder="请输入提成比例"
                                class="w-full"
                            />
                            <span class="text-sm text-gray-500 ml-2">%</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 权限配置 -->
            <div class="form-section">
                <h4 class="section-title">权限配置</h4>
                <el-form-item label="权限设置" prop="permissions">
                    <el-checkbox-group v-model="formData.permissions">
                        <el-row :gutter="20">
                            <el-col :span="8" v-for="permission in permissionOptions" :key="permission.value">
                                <el-checkbox :label="permission.value">{{ permission.label }}</el-checkbox>
                            </el-col>
                        </el-row>
                    </el-checkbox-group>
                </el-form-item>
            </div>

            <!-- 状态设置 -->
            <div class="form-section">
                <h4 class="section-title">状态设置</h4>
                <el-form-item label="员工状态" prop="status">
                    <el-radio-group v-model="formData.status">
                        <el-radio :label="1">在职</el-radio>
                        <el-radio :label="0">离职</el-radio>
                    </el-radio-group>
                </el-form-item>
            </div>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { addStoreStaff, editStoreStaff, getStoreOptions } from '@/addon/yy_phone_recycle/api/store'

interface Props {
    modelValue: boolean
    staffData?: any
    isEdit: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'complete'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const showDialog = ref(false)
const storeOptions = ref([])

// 权限选项
const permissionOptions = [
    { label: '订单管理', value: 'order_manage' },
    { label: '评估管理', value: 'evaluation_manage' },
    { label: '库存管理', value: 'inventory_manage' },
    { label: '客户管理', value: 'customer_manage' },
    { label: '财务查看', value: 'finance_view' },
    { label: '报表查看', value: 'report_view' },
    { label: '员工管理', value: 'staff_manage' },
    { label: '门店设置', value: 'store_setting' }
]

// 表单数据
const formData = reactive({
    store_id: 0,
    user_id: 0,
    staff_code: '',
    real_name: '',
    phone: '',
    id_card: '',
    position: '',
    role_type: 4,
    permissions: [],
    entry_date: '',
    salary_type: 1,
    base_salary: 0,
    commission_rate: 0,
    status: 1
})

// 表单验证规则
const formRules = {
    store_id: [
        { required: true, message: '请选择门店', trigger: 'change' }
    ],
    staff_code: [
        { required: true, message: '请输入员工编号', trigger: 'blur' },
        { max: 50, message: '员工编号长度不能超过50个字符', trigger: 'blur' }
    ],
    real_name: [
        { required: true, message: '请输入员工姓名', trigger: 'blur' },
        { max: 50, message: '员工姓名长度不能超过50个字符', trigger: 'blur' }
    ],
    phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    id_card: [
        { pattern: /^[1-9]\d{17}$/, message: '请输入正确的身份证号', trigger: 'blur' }
    ],
    position: [
        { required: true, message: '请输入职位', trigger: 'blur' },
        { max: 50, message: '职位长度不能超过50个字符', trigger: 'blur' }
    ],
    role_type: [
        { required: true, message: '请选择角色类型', trigger: 'change' }
    ],
    entry_date: [
        { required: true, message: '请选择入职日期', trigger: 'change' }
    ],
    salary_type: [
        { required: true, message: '请选择薪资类型', trigger: 'change' }
    ],
    base_salary: [
        { required: true, message: '请输入基础工资', trigger: 'blur' }
    ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
    showDialog.value = val
    if (val) {
        loadStoreOptions()
        if (props.isEdit && props.staffData) {
            // 编辑模式，填充数据
            Object.keys(formData).forEach(key => {
                if (props.staffData[key] !== undefined) {
                    if (key === 'permissions') {
                        // 处理权限数据格式转换
                        const permissions = props.staffData[key]
                        if (typeof permissions === 'object' && permissions !== null) {
                            // 如果是对象格式，转换为数组
                            formData[key] = Object.values(permissions)
                        } else if (Array.isArray(permissions)) {
                            // 如果已经是数组格式，直接使用
                            formData[key] = permissions
                        } else {
                            formData[key] = []
                        }
                    } else {
                        formData[key] = props.staffData[key]
                    }
                }
            })
        }
    }
})

watch(showDialog, (val) => {
    emit('update:modelValue', val)
})

/**
 * 加载门店选项
 */
const loadStoreOptions = async () => {
    try {
        const { data } = await getStoreOptions()
        storeOptions.value = data
    } catch (error) {
        console.error('加载门店选项失败:', error)
    }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
    if (!formRef.value) return
    
    try {
        await formRef.value.validate()
        submitting.value = true
        
        if (props.isEdit) {
            await editStoreStaff(props.staffData.id, formData)
            ElMessage.success('编辑员工成功')
        } else {
            await addStoreStaff(formData)
            ElMessage.success('添加员工成功')
        }
        
        emit('complete')
        handleClose()
    } catch (error) {
        console.error('提交失败:', error)
    } finally {
        submitting.value = false
    }
}

/**
 * 关闭弹窗
 */
const handleClose = () => {
    showDialog.value = false
    // 重置表单
    if (formRef.value) {
        formRef.value.resetFields()
    }
    Object.keys(formData).forEach(key => {
        if (typeof formData[key] === 'number') {
            formData[key] = key === 'role_type' ? 4 : (key === 'salary_type' || key === 'status') ? 1 : 0
        } else if (Array.isArray(formData[key])) {
            formData[key] = []
        } else {
            formData[key] = ''
        }
    })
}
</script>

<style lang="scss" scoped>
.form-section {
    margin-bottom: 20px;
    
    .section-title {
        margin: 0 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #e4e7ed;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
    }
}

.dialog-footer {
    text-align: right;
}

.w-full {
    width: 100%;
}
</style>
