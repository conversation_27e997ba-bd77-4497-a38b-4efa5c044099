<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <span class="text-page-title">会员地址管理</span>
                <el-button type="primary" @click="handleAdd">
                    添加地址
                </el-button>
            </div>

            <!-- 搜索表单 -->
            <el-card class="box-card !border-none my-[10px] table-search-wrap" shadow="never">
                <el-form :inline="true" :model="searchForm" ref="searchFormRef">
                    <el-form-item label="收货人">
                        <el-input v-model="searchForm.name" placeholder="请输入收货人姓名" clearable />
                    </el-form-item>
                    <el-form-item label="手机号">
                        <el-input v-model="searchForm.mobile" placeholder="请输入手机号" clearable />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="loadAddressList">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 地址列表 -->
            <div class="mt-4">
                <el-table :data="addressList" size="large" v-loading="loading">
                    <el-table-column prop="id" label="ID" min-width="80" />
                    <el-table-column prop="member.nickname" label="会员" min-width="120">
                        <template #default="{ row }">
                            <div>{{ row.member?.nickname || row.member?.username || '未知' }}</div>
                            <div class="text-xs text-gray-500">{{ row.member?.mobile }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="收货人" min-width="100" />
                    <el-table-column prop="mobile" label="手机号" min-width="120" />
                    <el-table-column prop="full_address" label="收货地址" min-width="200" show-overflow-tooltip />
                    <el-table-column prop="address_name" label="地址标签" min-width="100">
                        <template #default="{ row }">
                            <el-tag v-if="row.address_name" size="small" type="info">{{ row.address_name }}</el-tag>
                            <span v-else class="text-gray-400">-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="is_default" label="默认地址" min-width="100">
                        <template #default="{ row }">
                            <el-tag :type="row.is_default === 1 ? 'success' : 'info'" size="small">
                                {{ row.is_default === 1 ? '默认' : '普通' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" fixed="right" min-width="200">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="viewAddress(row)">查看</el-button>
                            <el-button type="primary" link @click="editAddress(row)">编辑</el-button>
                            <el-button 
                                v-if="row.is_default !== 1"
                                type="warning" 
                                link 
                                @click="setDefault(row)"
                            >
                                设为默认
                            </el-button>
                            <el-button type="danger" link @click="deleteAddress(row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="flex justify-end mt-4">
                    <el-pagination
                        v-model:current-page="pageData.page"
                        v-model:page-size="pageData.limit"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pageData.total"
                        @size-change="loadAddressList"
                        @current-change="loadAddressList"
                    />
                </div>
            </div>
        </el-card>

        <!-- 地址详情弹窗 -->
        <el-dialog
            v-model="showDetailDialog"
            title="地址详情"
            width="600px"
            :destroy-on-close="true"
        >
            <address-detail
                v-if="showDetailDialog"
                :address-data="currentAddress"
                @close="showDetailDialog = false"
            />
        </el-dialog>

        <!-- 添加/编辑地址弹窗 -->
        <address-add-edit 
            v-model="showAddEditDialog" 
            :address-data="currentAddress"
            :is-edit="isEdit"
            @complete="handleAddEditComplete"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMemberAddressList, deleteMemberAddress, setDefaultMemberAddress, getMemberOptions } from '@/addon/yy_phone_recycle/api/member'
import AddressDetail from './components/address-detail.vue'
import AddressAddEdit from './components/address-add-edit.vue'

const loading = ref(false)
const showDetailDialog = ref(false)
const showAddEditDialog = ref(false)
const isEdit = ref(false)
const currentAddress = ref(null)
const searchFormRef = ref()
const memberOptions = ref([])

// 搜索表单
const searchForm = reactive({
    member_id: '',
    name: '',
    mobile: '',
    full_address: '',
    is_default: ''
})

// 分页数据
const pageData = reactive({
    page: 1,
    limit: 10,
    total: 0
})

// 地址列表
const addressList = ref([])

/**
 * 加载会员选项
 */
const loadMemberOptions = async () => {
    try {
        const { data } = await getMemberOptions()
        memberOptions.value = data
    } catch (error) {
        console.error('加载会员选项失败:', error)
    }
}

/**
 * 加载地址列表
 */
const loadAddressList = async () => {
    loading.value = true
    try {
        const params = {
            ...searchForm,
            page: pageData.page,
            limit: pageData.limit
        }
        const { data } = await getMemberAddressList(params)
        addressList.value = data.data
        pageData.total = data.total
    } catch (error) {
        console.error('加载地址列表失败:', error)
        ElMessage.error('加载地址列表失败')
    } finally {
        loading.value = false
    }
}

/**
 * 重置搜索
 */
const resetSearch = () => {
    searchFormRef.value?.resetFields()
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
    })
    pageData.page = 1
    loadAddressList()
}

/**
 * 查看地址详情
 */
const viewAddress = (address: any) => {
    currentAddress.value = address
    showDetailDialog.value = true
}

/**
 * 添加地址
 */
const handleAdd = () => {
    currentAddress.value = null
    isEdit.value = false
    showAddEditDialog.value = true
}

/**
 * 编辑地址
 */
const editAddress = (address: any) => {
    currentAddress.value = address
    isEdit.value = true
    showAddEditDialog.value = true
}

/**
 * 添加/编辑完成
 */
const handleAddEditComplete = () => {
    loadAddressList()
}

/**
 * 设置默认地址
 */
const setDefault = (address: any) => {
    ElMessageBox.confirm(
        `确定要将"${address.name}"的地址设置为默认地址吗？`,
        '设置默认地址',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(async () => {
        try {
            await setDefaultMemberAddress(address.id)
            ElMessage.success('设置成功')
            loadAddressList()
        } catch (error) {
            console.error('设置默认地址失败:', error)
        }
    })
}

/**
 * 删除地址
 */
const deleteAddress = (address: any) => {
    ElMessageBox.confirm(
        `确定要删除"${address.name}"的地址吗？此操作不可恢复！`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(async () => {
        try {
            await deleteMemberAddress(address.id)
            ElMessage.success('删除成功')
            loadAddressList()
        } catch (error) {
            console.error('删除地址失败:', error)
        }
    })
}

onMounted(() => {
    loadMemberOptions()
    loadAddressList()
})
</script>

<style lang="scss" scoped>
.table-search-wrap {
    .el-form {
        .el-form-item {
            margin-bottom: 16px;
        }
    }
}
</style>
