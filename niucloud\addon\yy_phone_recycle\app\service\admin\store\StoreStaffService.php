<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\service\admin\store;

use addon\yy_phone_recycle\app\model\store\StoreStaff;
use core\base\BaseAdminService;

/**
 * 门店员工服务层
 * Class StoreStaffService
 * @package addon\yy_phone_recycle\app\service\admin\store
 */
class StoreStaffService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new StoreStaff();
    }

    /**
     * 获取员工分页列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id,store_id,user_id,staff_code,real_name,phone,id_card,position,role_type,permissions,entry_date,salary_type,base_salary,commission_rate,status,create_time,update_time';
        $order = 'role_type asc, id desc';

        $search_model = $this->model
            ->withSearch(["store_id", "real_name", "role_type", "status"], $where)
            ->with([
                'user' => function($query) {
                    $query->field('uid,username,real_name');
                },
                'store' => function($query) {
                    $query->field('id,store_name');
                }
            ])
            ->field($field)
            ->order($order);

        $result = $this->pageQuery($search_model);

        // 处理列表中的权限数据，确保返回数组格式
        if (isset($result['data']) && is_array($result['data'])) {
            foreach ($result['data'] as &$item) {
                if (isset($item['permissions']) && is_array($item['permissions'])) {
                    // 检查是否是关联数组（对象格式），如果是则转换为索引数组
                    $keys = array_keys($item['permissions']);
                    if ($keys !== range(0, count($keys) - 1)) {
                        $item['permissions'] = array_values($item['permissions']);
                    }
                }
            }
        }

        return $result;
    }

    /**
     * 获取员工信息
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = 'id,store_id,user_id,staff_code,real_name,phone,id_card,position,role_type,permissions,entry_date,salary_type,base_salary,commission_rate,status,create_time,update_time';

        $info = $this->model->field($field)
            ->with([
                'user' => function($query) {
                    $query->field('uid,username,real_name');
                },
                'store' => function($query) {
                    $query->field('id,store_name');
                }
            ])
            ->where([['id', '=', $id]])
            ->findOrEmpty();

        if ($info->isEmpty()) {
            throw new \Exception('STAFF_NOT_FOUND');
        }

        $data = $info->toArray();

        // 处理权限数据，确保返回数组格式
        if (isset($data['permissions']) && is_array($data['permissions'])) {
            // 检查是否是关联数组（对象格式），如果是则转换为索引数组
            $keys = array_keys($data['permissions']);
            if ($keys !== range(0, count($keys) - 1)) {
                $data['permissions'] = array_values($data['permissions']);
            }
        }

        // 获取工作统计
        $data['work_stats'] = $this->getWorkStats($id);

        return $data;
    }

    /**
     * 添加员工
     * @param array $data
     * @return mixed
     */
    public function add(array $data)
    {
        // 生成员工编号
        if (empty($data['staff_code'])) {
            $data['staff_code'] = $this->generateStaffCode($data['store_id']);
        }

        // 处理权限数据
        if (isset($data['permissions']) && is_array($data['permissions'])) {
            $data['permissions'] = json_encode($data['permissions']);
        }

        $res = $this->model->save($data);
        return $this->model->id;
    }

    /**
     * 编辑员工
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function edit(int $id, array $data)
    {
        $info = $this->model->findOrEmpty($id);
        if ($info->isEmpty()) {
            throw new \Exception('STAFF_NOT_FOUND');
        }

        // 处理权限数据
        if (isset($data['permissions']) && is_array($data['permissions'])) {
            $data['permissions'] = json_encode($data['permissions']);
        }

        $res = $info->save($data);
        return $res !== false;
    }

    /**
     * 删除员工
     * @param int $id
     * @return bool
     */
    public function del(int $id)
    {
        $info = $this->model->findOrEmpty($id);
        if ($info->isEmpty()) {
            throw new \Exception('STAFF_NOT_FOUND');
        }

        $res = $info->delete();
        return $res !== false;
    }

    /**
     * 修改员工状态
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function modifyStatus(int $id, array $data)
    {
        $info = $this->model->findOrEmpty($id);
        if ($info->isEmpty()) {
            throw new \Exception('STAFF_NOT_FOUND');
        }

        $res = $info->save(['status' => $data['status']]);
        return $res !== false;
    }

    /**
     * 生成员工编号
     * @param int $store_id
     * @return string
     */
    private function generateStaffCode(int $store_id): string
    {
        $prefix = 'S' . str_pad($store_id, 3, '0', STR_PAD_LEFT);
        $count = $this->model->where('store_id', $store_id)->count();
        $suffix = str_pad($count + 1, 4, '0', STR_PAD_LEFT);
        return $prefix . $suffix;
    }

    /**
     * 获取员工工作统计
     * @param int $staff_id
     * @return array
     */
    private function getWorkStats(int $staff_id): array
    {
        // 这里可以根据实际业务需求统计员工的工作数据
        // 比如处理的订单数量、金额等
        return [
            'total_orders' => 0,
            'total_amount' => 0,
            'this_month_orders' => 0,
            'this_month_amount' => 0
        ];
    }
}
