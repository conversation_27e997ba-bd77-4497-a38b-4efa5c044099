<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\service\admin\store;

use addon\yy_phone_recycle\app\model\store\StoreStaff;
use core\base\BaseAdminService;

/**
 * 门店员工服务层
 * Class StoreStaffService
 * @package addon\yy_phone_recycle\app\service\admin\store
 */
class StoreStaffService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new StoreStaff();
    }

    /**
     * 获取员工分页列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id,store_id,member_id,staff_code,real_name,phone,id_card,entry_date,status,create_time,update_time';
        $order = 'create_time desc, id desc';

        $search_model = $this->model
            ->withSearch(["store_id", "real_name", "member_id", "status"], $where)
            ->with([
                'store' => function($query) {
                    $query->field('id,store_name');
                },
                'member' => function($query) {
                    $query->field('member_id,username,nickname,mobile');
                }
            ])
            ->field($field)
            ->order($order);

        return $this->pageQuery($search_model);
    }

    /**
     * 获取员工信息
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = 'id,store_id,member_id,staff_code,real_name,phone,id_card,entry_date,status,create_time,update_time';

        $info = $this->model->field($field)
            ->with([
                'store' => function($query) {
                    $query->field('id,store_name');
                },
                'member' => function($query) {
                    $query->field('member_id,username,nickname,mobile');
                }
            ])
            ->where([['id', '=', $id]])
            ->findOrEmpty();

        if ($info->isEmpty()) {
            throw new \Exception('STAFF_NOT_FOUND');
        }

        return $info->toArray();
    }

    /**
     * 添加员工
     * @param array $data
     * @return mixed
     */
    public function add(array $data)
    {
        // 检查会员是否已经是其他门店的员工
        if (!empty($data['member_id'])) {
            $exists = $this->model->where('member_id', $data['member_id'])
                ->where('status', 1)
                ->findOrEmpty();
            if (!$exists->isEmpty()) {
                throw new \Exception('MEMBER_ALREADY_STAFF');
            }
        }

        // 生成员工编号
        if (empty($data['staff_code'])) {
            $data['staff_code'] = $this->generateStaffCode($data['store_id']);
        }

        $res = $this->model->save($data);
        return $this->model->id;
    }

    /**
     * 编辑员工
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function edit(int $id, array $data)
    {
        $info = $this->model->findOrEmpty($id);
        if ($info->isEmpty()) {
            throw new \Exception('STAFF_NOT_FOUND');
        }

        // 检查会员是否已经是其他门店的员工（排除当前员工）
        if (!empty($data['member_id']) && $data['member_id'] != $info->member_id) {
            $exists = $this->model->where('member_id', $data['member_id'])
                ->where('status', 1)
                ->where('id', '<>', $id)
                ->findOrEmpty();
            if (!$exists->isEmpty()) {
                throw new \Exception('MEMBER_ALREADY_STAFF');
            }
        }

        $res = $info->save($data);
        return $res !== false;
    }

    /**
     * 删除员工
     * @param int $id
     * @return bool
     */
    public function del(int $id)
    {
        $info = $this->model->findOrEmpty($id);
        if ($info->isEmpty()) {
            throw new \Exception('STAFF_NOT_FOUND');
        }

        $res = $info->delete();
        return $res !== false;
    }

    /**
     * 修改员工状态
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function modifyStatus(int $id, array $data)
    {
        $info = $this->model->findOrEmpty($id);
        if ($info->isEmpty()) {
            throw new \Exception('STAFF_NOT_FOUND');
        }

        $res = $info->save(['status' => $data['status']]);
        return $res !== false;
    }

    /**
     * 生成员工编号
     * @param int $store_id
     * @return string
     */
    private function generateStaffCode(int $store_id): string
    {
        $prefix = 'S' . str_pad($store_id, 3, '0', STR_PAD_LEFT);
        $count = $this->model->where('store_id', $store_id)->count();
        $suffix = str_pad($count + 1, 4, '0', STR_PAD_LEFT);
        return $prefix . $suffix;
    }
}
