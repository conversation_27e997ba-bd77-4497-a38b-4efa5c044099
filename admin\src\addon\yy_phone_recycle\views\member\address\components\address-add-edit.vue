<template>
    <el-dialog
        v-model="showDialog"
        :title="isEdit ? '编辑地址' : '添加地址'"
        width="800px"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            v-loading="loading"
        >
            <!-- 会员信息 -->
            <div class="form-section">
                <h4 class="section-title">会员信息</h4>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="选择会员" prop="member_id">
                            <el-select 
                                v-model="formData.member_id" 
                                placeholder="请选择会员" 
                                class="w-full"
                                filterable
                                :disabled="isEdit"
                            >
                                <el-option
                                    v-for="member in memberOptions"
                                    :key="member.member_id"
                                    :label="`${member.nickname || member.username}(${member.mobile})`"
                                    :value="member.member_id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 收货信息 -->
            <div class="form-section">
                <h4 class="section-title">收货信息</h4>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="收货人" prop="name">
                            <el-input v-model="formData.name" placeholder="请输入收货人姓名" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手机号" prop="mobile">
                            <el-input v-model="formData.mobile" placeholder="请输入手机号" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 地址信息 -->
            <div class="form-section">
                <h4 class="section-title">地址信息</h4>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="省份" prop="province_id">
                            <el-select 
                                v-model="formData.province_id" 
                                placeholder="请选择省份" 
                                class="w-full"
                                @change="handleProvinceChange"
                            >
                                <el-option
                                    v-for="province in provinceOptions"
                                    :key="province.id"
                                    :label="province.name"
                                    :value="province.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="城市" prop="city_id">
                            <el-select 
                                v-model="formData.city_id" 
                                placeholder="请选择城市" 
                                class="w-full"
                                @change="handleCityChange"
                            >
                                <el-option
                                    v-for="city in cityOptions"
                                    :key="city.id"
                                    :label="city.name"
                                    :value="city.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="区县" prop="district_id">
                            <el-select 
                                v-model="formData.district_id" 
                                placeholder="请选择区县" 
                                class="w-full"
                            >
                                <el-option
                                    v-for="district in districtOptions"
                                    :key="district.id"
                                    :label="district.name"
                                    :value="district.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="详细地址" prop="address">
                            <el-input
                                v-model="formData.address"
                                type="textarea"
                                :rows="3"
                                placeholder="请输入详细地址"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="地址标签" prop="address_name">
                            <el-input v-model="formData.address_name" placeholder="如：家、公司、学校等" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="默认地址" prop="is_default">
                            <el-radio-group v-model="formData.is_default">
                                <el-radio :label="1">是</el-radio>
                                <el-radio :label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 位置信息 -->
            <div class="form-section">
                <h4 class="section-title">位置信息（可选）</h4>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="经度" prop="lng">
                            <el-input v-model="formData.lng" placeholder="请输入经度" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="纬度" prop="lat">
                            <el-input v-model="formData.lat" placeholder="请输入纬度" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { addMemberAddress, editMemberAddress, getMemberOptions } from '@/addon/yy_phone_recycle/api/member'
import { getAreaList } from '@/api/common'

interface Props {
    modelValue: boolean
    addressData?: any
    isEdit: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'complete'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const showDialog = ref(false)

// 选项数据
const memberOptions = ref([])
const provinceOptions = ref([])
const cityOptions = ref([])
const districtOptions = ref([])

// 表单数据
const formData = reactive({
    member_id: 0,
    name: '',
    mobile: '',
    province_id: 0,
    city_id: 0,
    district_id: 0,
    address: '',
    address_name: '',
    lng: '',
    lat: '',
    is_default: 0
})

// 表单验证规则
const formRules = {
    member_id: [
        { required: true, message: '请选择会员', trigger: 'change' }
    ],
    name: [
        { required: true, message: '请输入收货人姓名', trigger: 'blur' },
        { max: 50, message: '收货人姓名长度不能超过50个字符', trigger: 'blur' }
    ],
    mobile: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    province_id: [
        { required: true, message: '请选择省份', trigger: 'change' }
    ],
    city_id: [
        { required: true, message: '请选择城市', trigger: 'change' }
    ],
    district_id: [
        { required: true, message: '请选择区县', trigger: 'change' }
    ],
    address: [
        { required: true, message: '请输入详细地址', trigger: 'blur' },
        { max: 255, message: '详细地址长度不能超过255个字符', trigger: 'blur' }
    ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
    showDialog.value = val
    if (val) {
        loadOptions()
        if (props.isEdit && props.addressData) {
            // 编辑模式，填充数据
            Object.keys(formData).forEach(key => {
                if (props.addressData[key] !== undefined) {
                    formData[key] = props.addressData[key]
                }
            })
            // 加载对应的城市和区县选项
            if (formData.province_id) {
                loadCityOptions(formData.province_id)
            }
            if (formData.city_id) {
                loadDistrictOptions(formData.city_id)
            }
        }
    }
})

watch(showDialog, (val) => {
    emit('update:modelValue', val)
})

/**
 * 加载选项数据
 */
const loadOptions = async () => {
    try {
        // 加载会员选项
        const memberRes = await getMemberOptions()
        memberOptions.value = memberRes.data

        // 加载省份选项
        const provinceRes = await getAreaList({ pid: 0 })
        provinceOptions.value = provinceRes.data
    } catch (error) {
        console.error('加载选项数据失败:', error)
        ElMessage.error('加载数据失败')
    }
}

/**
 * 省份变化处理
 */
const handleProvinceChange = (provinceId: number) => {
    formData.city_id = 0
    formData.district_id = 0
    cityOptions.value = []
    districtOptions.value = []
    
    if (provinceId) {
        loadCityOptions(provinceId)
    }
}

/**
 * 城市变化处理
 */
const handleCityChange = (cityId: number) => {
    formData.district_id = 0
    districtOptions.value = []
    
    if (cityId) {
        loadDistrictOptions(cityId)
    }
}

/**
 * 加载城市选项
 */
const loadCityOptions = async (provinceId: number) => {
    try {
        const { data } = await getAreaList({ pid: provinceId })
        cityOptions.value = data
    } catch (error) {
        console.error('加载城市选项失败:', error)
    }
}

/**
 * 加载区县选项
 */
const loadDistrictOptions = async (cityId: number) => {
    try {
        const { data } = await getAreaList({ pid: cityId })
        districtOptions.value = data
    } catch (error) {
        console.error('加载区县选项失败:', error)
    }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
    if (!formRef.value) return
    
    try {
        await formRef.value.validate()
        submitting.value = true
        
        if (props.isEdit) {
            await editMemberAddress(props.addressData.id, formData)
            ElMessage.success('编辑地址成功')
        } else {
            await addMemberAddress(formData)
            ElMessage.success('添加地址成功')
        }
        
        emit('complete')
        handleClose()
    } catch (error) {
        console.error('提交失败:', error)
    } finally {
        submitting.value = false
    }
}

/**
 * 关闭弹窗
 */
const handleClose = () => {
    showDialog.value = false
    // 重置表单
    if (formRef.value) {
        formRef.value.resetFields()
    }
    Object.keys(formData).forEach(key => {
        if (typeof formData[key] === 'number') {
            formData[key] = 0
        } else {
            formData[key] = ''
        }
    })
    
    // 清空选项
    cityOptions.value = []
    districtOptions.value = []
}
</script>

<style lang="scss" scoped>
.form-section {
    margin-bottom: 20px;
    
    .section-title {
        margin: 0 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #e4e7ed;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
    }
}

.dialog-footer {
    text-align: right;
}

.w-full {
    width: 100%;
}
</style>
