<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\adminapi\controller\member;

use addon\yy_phone_recycle\app\service\admin\member\MemberAddressService;
use core\base\BaseAdminController;
use think\Response;

/**
 * 会员地址控制器
 * Class MemberAddress
 * @package addon\yy_phone_recycle\app\adminapi\controller\member
 */
class MemberAddress extends BaseAdminController
{
    /**
     * 获取会员地址列表
     * @return Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ["member_id", ""],
            ["name", ""],
            ["mobile", ""],
            ["province_id", ""],
            ["city_id", ""],
            ["is_default", ""],
            ["full_address", ""],
        ]);
        return success((new MemberAddressService())->getPage($data));
    }

    /**
     * 会员地址详情
     * @param int $id
     * @return Response
     */
    public function info(int $id)
    {
        return success((new MemberAddressService())->getInfo($id));
    }

    /**
     * 添加会员地址
     * @return Response
     */
    public function add()
    {
        $data = $this->request->params([
            ["member_id", 0],
            ["name", ""],
            ["mobile", ""],
            ["province_id", 0],
            ["city_id", 0],
            ["district_id", 0],
            ["address", ""],
            ["address_name", ""],
            ["lng", ""],
            ["lat", ""],
            ["is_default", 0],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\member\MemberAddress.add');
        $id = (new MemberAddressService())->add($data);
        return success('ADD_SUCCESS', ['id' => $id]);
    }

    /**
     * 会员地址编辑
     * @param int $id
     * @return Response
     */
    public function edit(int $id)
    {
        $data = $this->request->params([
            ["member_id", 0],
            ["name", ""],
            ["mobile", ""],
            ["province_id", 0],
            ["city_id", 0],
            ["district_id", 0],
            ["address", ""],
            ["address_name", ""],
            ["lng", ""],
            ["lat", ""],
            ["is_default", 0],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\member\MemberAddress.edit');
        (new MemberAddressService())->edit($id, $data);
        return success('EDIT_SUCCESS');
    }

    /**
     * 会员地址删除
     * @param int $id
     * @return Response
     */
    public function del(int $id)
    {
        (new MemberAddressService())->del($id);
        return success('DELETE_SUCCESS');
    }

    /**
     * 设置默认地址
     * @param int $id
     * @return Response
     */
    public function setDefault(int $id)
    {
        (new MemberAddressService())->setDefault($id);
        return success('SET_SUCCESS');
    }

    /**
     * 获取会员选项
     * @return Response
     */
    public function memberOptions()
    {
        return success((new MemberAddressService())->getMemberOptions());
    }
}
