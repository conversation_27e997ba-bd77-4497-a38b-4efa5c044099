<template>
    <div class="staff-manage">
        <!-- 搜索条件 -->
        <div class="navbar mb-4">
            <div class="navbar-left">
                <el-input v-model="searchParam.real_name" clearable placeholder="请输入员工姓名" class="input-width" />
                <el-input v-model="searchParam.member_id" clearable placeholder="请输入会员ID" class="input-width" />
                <el-select v-model="searchParam.status" clearable placeholder="员工状态" class="input-width">
                    <el-option label="在职" :value="1" />
                    <el-option label="离职" :value="0" />
                </el-select>
                <el-button type="primary" @click="loadStaffList()">搜索</el-button>
                <el-button @click="resetSearch">重置</el-button>
            </div>
            <div class="navbar-right">
                <el-button type="primary" @click="addStaff">
                    <template #icon>
                        <icon name="element-Plus" />
                    </template>
                    添加员工
                </el-button>
            </div>
        </div>

        <!-- 员工列表 -->
        <el-table :data="staffList" v-loading="loading" size="large">
            <template #empty>
                <span>暂无员工数据</span>
            </template>
            <el-table-column prop="staff_code" label="员工编号" min-width="120" />
            <el-table-column prop="real_name" label="姓名" min-width="100" />
            <el-table-column prop="phone" label="手机号" min-width="120" />
            <el-table-column prop="member_id" label="会员ID" min-width="100" />
            <el-table-column prop="member.nickname" label="会员昵称" min-width="120" />
            <el-table-column prop="entry_date" label="入职日期" min-width="120" />
            <el-table-column prop="status" label="状态" min-width="80" align="center">
                <template #default="{ row }">
                    <el-switch
                        v-model="row.status"
                        :active-value="1"
                        :inactive-value="0"
                        @change="editStaffStatus(row)"
                    />
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" align="right" min-width="120">
                <template #default="{ row }">
                    <el-button type="primary" link @click="editStaff(row)">编辑</el-button>
                    <el-button type="danger" link @click="deleteStaff(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="mt-4 flex justify-end">
            <el-pagination
                v-model:current-page="page"
                v-model:page-size="limit"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="loadStaffList()"
                @current-change="loadStaffList"
            />
        </div>

        <!-- 员工编辑弹窗 -->
        <el-dialog v-model="showStaffDialog" :title="staffForm.id ? '编辑员工' : '添加员工'" width="600px" :destroy-on-close="true">
            <el-form :model="staffForm" label-width="100px" ref="staffFormRef" :rules="staffFormRules">
                <el-form-item label="会员ID" prop="member_id">
                    <el-input v-model="staffForm.member_id" clearable placeholder="请输入会员ID" />
                    <div class="text-gray-500 text-sm mt-1">请输入系统中已存在的会员ID</div>
                </el-form-item>
                <el-form-item label="员工姓名" prop="real_name">
                    <el-input v-model="staffForm.real_name" clearable placeholder="请输入员工姓名" />
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                    <el-input v-model="staffForm.phone" clearable placeholder="请输入手机号" />
                </el-form-item>
                <el-form-item label="身份证号" prop="id_card">
                    <el-input v-model="staffForm.id_card" clearable placeholder="请输入身份证号（可选）" />
                </el-form-item>
                <el-form-item label="入职日期" prop="entry_date">
                    <el-date-picker
                        v-model="staffForm.entry_date"
                        type="date"
                        placeholder="请选择入职日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                    />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="staffForm.status">
                        <el-radio :label="1">在职</el-radio>
                        <el-radio :label="0">离职</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showStaffDialog = false">取消</el-button>
                <el-button type="primary" :loading="staffFormLoading" @click="confirmStaff">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getStoreStaff, addStoreStaff, editStoreStaff, deleteStoreStaff, editStoreStaffStatus } from '@/addon/yy_phone_recycle/api/store'

const props = defineProps<{
    storeId: number
}>()

const emit = defineEmits(['close'])

// 响应式数据
const loading = ref(false)
const staffList = ref([])
const page = ref(1)
const limit = ref(10)
const total = ref(0)
const showStaffDialog = ref(false)
const staffFormLoading = ref(false)
const staffFormRef = ref()

// 搜索参数
const searchParam = reactive({
    real_name: '',
    member_id: '',
    status: ''
})

// 员工表单
const staffForm = reactive({
    id: '',
    member_id: '',
    real_name: '',
    phone: '',
    id_card: '',
    entry_date: '',
    status: 1
})

// 表单验证规则
const staffFormRules = {
    member_id: [
        { required: true, message: '请输入会员ID', trigger: 'blur' }
    ],
    real_name: [
        { required: true, message: '请输入员工姓名', trigger: 'blur' }
    ],
    phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    entry_date: [
        { required: true, message: '请选择入职日期', trigger: 'change' }
    ]
}

/**
 * 加载员工列表
 */
const loadStaffList = async (pageNum: number = 1) => {
    loading.value = true
    try {
        const params = {
            page: pageNum > 0 ? pageNum : page.value,
            limit: limit.value,
            ...searchParam
        }
        const res = await getStoreStaff(props.storeId, params)
        staffList.value = res.data.data
        total.value = res.data.total
        page.value = res.data.page
    } catch (error) {
        console.error('获取员工列表失败:', error)
        ElMessage.error('获取员工列表失败')
    } finally {
        loading.value = false
    }
}

/**
 * 重置搜索
 */
const resetSearch = () => {
    Object.assign(searchParam, {
        real_name: '',
        member_id: '',
        status: ''
    })
    loadStaffList()
}

/**
 * 添加员工
 */
const addStaff = () => {
    Object.assign(staffForm, {
        id: '',
        member_id: '',
        real_name: '',
        phone: '',
        id_card: '',
        entry_date: '',
        status: 1
    })
    showStaffDialog.value = true
}

/**
 * 编辑员工
 */
const editStaff = (data: any) => {
    Object.assign(staffForm, {
        id: data.id,
        member_id: data.member_id,
        real_name: data.real_name,
        phone: data.phone,
        id_card: data.id_card,
        entry_date: data.entry_date,
        status: data.status
    })
    showStaffDialog.value = true
}

/**
 * 确认提交员工信息
 */
const confirmStaff = async () => {
    try {
        await staffFormRef.value.validate()
        staffFormLoading.value = true
        
        if (staffForm.id) {
            await editStoreStaff(props.storeId, staffForm.id, staffForm)
            ElMessage.success('编辑成功')
        } else {
            await addStoreStaff(props.storeId, staffForm)
            ElMessage.success('添加成功')
        }
        
        showStaffDialog.value = false
        loadStaffList()
    } catch (error) {
        console.error('操作失败:', error)
    } finally {
        staffFormLoading.value = false
    }
}

/**
 * 删除员工
 */
const deleteStaff = (data: any) => {
    ElMessageBox.confirm('确定要删除该员工吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            await deleteStoreStaff(props.storeId, data.id)
            ElMessage.success('删除成功')
            loadStaffList()
        } catch (error) {
            console.error('删除失败:', error)
        }
    })
}

/**
 * 修改员工状态
 */
const editStaffStatus = async (data: any) => {
    try {
        await editStoreStaffStatus(props.storeId, data.id, { status: data.status })
        ElMessage.success('状态修改成功')
    } catch (error) {
        console.error('状态修改失败:', error)
        // 恢复原状态
        data.status = data.status === 1 ? 0 : 1
    }
}

onMounted(() => {
    loadStaffList()
})
</script>

<style lang="scss" scoped>
.input-width {
    width: 180px;
}
</style>
