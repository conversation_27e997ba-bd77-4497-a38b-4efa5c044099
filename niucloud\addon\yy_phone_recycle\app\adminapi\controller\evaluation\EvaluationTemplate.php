<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\adminapi\controller\evaluation;

use core\base\BaseAdminController;
use addon\yy_phone_recycle\app\service\admin\evaluation\EvaluationTemplateService;
use think\Response;

/**
 * 估价模板控制器
 * Class EvaluationTemplate
 * @package addon\yy_phone_recycle\app\adminapi\controller\evaluation
 */
class EvaluationTemplate extends BaseAdminController
{
    /**
     * 获取估价模板列表
     * @return Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ["name", ""],
            ["model_id", ""],
            ["status", ""],
            ["question_count_filter", ""],
        ]);
        return success((new EvaluationTemplateService())->getPage($data));
    }

    /**
     * 获取估价模板详情
     * @param int $id
     * @return Response
     */
    public function info(int $id)
    {
        return success((new EvaluationTemplateService())->getInfo($id));
    }

    /**
     * 添加估价模板
     * @return Response
     */
    public function add()
    {
        $data = $this->request->params([
            ["name", ""],
            ["model_id", 0],
            ["base_price", 0.00],
            ["min_price", 0.00],
            ["description", ""],
            ["status", 1],
            ["sort", 0],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\evaluation\EvaluationTemplate.add');
        $id = (new EvaluationTemplateService())->add($data);
        return success('ADD_SUCCESS', ['id' => $id]);
    }

    /**
     * 编辑估价模板
     * @param int $id
     * @return Response
     */
    public function edit(int $id)
    {
        $data = $this->request->params([
            ["name", ""],
            ["model_id", 0],
            ["base_price", 0.00],
            ["min_price", 0.00],
            ["description", ""],
            ["status", 1],
            ["sort", 0],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\evaluation\EvaluationTemplate.edit');
        (new EvaluationTemplateService())->edit($id, $data);
        return success('EDIT_SUCCESS');
    }

    /**
     * 删除估价模板
     * @param int $id
     * @return Response
     */
    public function del(int $id)
    {
        (new EvaluationTemplateService())->del($id);
        return success('DELETE_SUCCESS');
    }

    /**
     * 复制估价模板
     * @param int $id
     * @return Response
     */
    public function copy(int $id)
    {
        $data = $this->request->params([
            ["name", ""],
            ["model_id", 0],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\evaluation\EvaluationTemplate.copy');
        $new_id = (new EvaluationTemplateService())->copy($id, $data);
        return success('COPY_SUCCESS', ['id' => $new_id]);
    }

    /**
     * 修改模板状态
     * @param int $id
     * @return Response
     */
    public function modifyStatus(int $id)
    {
        $data = $this->request->params([
            ["status", 1],
        ]);
        (new EvaluationTemplateService())->modifyStatus($id, $data['status']);
        return success('MODIFY_SUCCESS');
    }

    /**
     * 获取手机型号列表
     * @return Response
     */
    public function getPhoneModelList()
    {
        return success((new EvaluationTemplateService())->getPhoneModelList());
    }

    /**
     * 获取可复制的模板列表
     * @return Response
     */
    public function getCopyableTemplates()
    {
        $exclude_model_id = $this->request->param('exclude_model_id', 0);
        return success((new EvaluationTemplateService())->getCopyableTemplates($exclude_model_id));
    }

    /**
     * 获取模板统计信息
     * @param int $id
     * @return Response
     */
    public function getStatistics(int $id)
    {
        return success((new EvaluationTemplateService())->getStatistics($id));
    }

    /**
     * 导出模板配置
     * @param int $id
     * @return Response
     */
    public function exportTemplate(int $id)
    {
        $data = (new EvaluationTemplateService())->exportTemplate($id);
        return success($data);
    }

    /**
     * 导入模板配置
     * @return Response
     */
    public function importTemplate()
    {
        $data = $this->request->params([
            ["template_data", []],
            ["model_id", 0],
        ]);
        $this->validate($data, 'addon\yy_phone_recycle\app\validate\evaluation\EvaluationTemplate.import');
        $id = (new EvaluationTemplateService())->importTemplate($data['template_data'], $data['model_id']);
        return success('IMPORT_SUCCESS', ['id' => $id]);
    }

    /**
     * 获取评估模板选项
     * @return Response
     */
    public function options()
    {
        return success((new EvaluationTemplateService())->getOptions());
    }
}
