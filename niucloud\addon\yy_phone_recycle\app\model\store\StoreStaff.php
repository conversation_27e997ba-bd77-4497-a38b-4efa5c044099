<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\model\store;

use core\base\BaseModel;

/**
 * 门店员工模型
 * Class StoreStaff
 * @package addon\yy_phone_recycle\app\model\store
 */
class StoreStaff extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yy_phone_recycle_store_staff';

    /**
     * 自动写入时间戳字段
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 数据表字段信息
     * @var array
     */
    protected $schema = [
        'id' => 'int',
        'store_id' => 'int',
        'member_id' => 'int',
        'staff_code' => 'string',
        'real_name' => 'string',
        'phone' => 'string',
        'id_card' => 'string',
        'entry_date' => 'date',
        'status' => 'int',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
    ];



    /**
     * 搜索器:门店ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchStoreIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('store_id', '=', $value);
        }
    }

    /**
     * 搜索器:员工姓名
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchRealNameAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('real_name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器:会员ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchMemberIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('member_id', '=', $value);
        }
    }

    /**
     * 搜索器:状态
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('status', '=', $value);
        }
    }



    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        return $data['status'] == 1 ? '在职' : '离职';
    }

    /**
     * 关联门店
     * @return \think\model\relation\BelongsTo
     */
    public function store()
    {
        return $this->belongsTo('addon\yy_phone_recycle\app\model\store\Store', 'store_id', 'id');
    }

    /**
     * 关联会员
     * @return \think\model\relation\BelongsTo
     */
    public function member()
    {
        return $this->belongsTo('app\model\member\Member', 'member_id', 'member_id')
            ->field('member_id,username,nickname,mobile');
    }
}
