<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\validate\store;

use core\base\BaseValidate;

/**
 * 门店员工验证器
 * Class StoreStaff
 * @package addon\yy_phone_recycle\app\validate\store
 */
class StoreStaff extends BaseValidate
{
    protected $rule = [
        'store_id' => 'require|gt:0',
        'user_id' => 'egt:0',
        'staff_code' => 'require|length:1,50',
        'real_name' => 'require|length:1,50',
        'phone' => 'require|mobile',
        'id_card' => 'length:18',
        'position' => 'require|length:1,50',
        'role_type' => 'require|in:1,2,3,4',
        'permissions' => 'array',
        'entry_date' => 'require|date',
        'salary_type' => 'require|in:1,2,3',
        'base_salary' => 'require|egt:0',
        'commission_rate' => 'egt:0|elt:100',
        'status' => 'in:0,1',
    ];

    protected $message = [
        'store_id.require' => '请选择门店',
        'store_id.gt' => '请选择门店',
        'user_id.egt' => '用户ID格式错误',
        'staff_code.require' => '员工编号不能为空',
        'staff_code.length' => '员工编号长度不能超过50个字符',
        'real_name.require' => '员工姓名不能为空',
        'real_name.length' => '员工姓名长度不能超过50个字符',
        'phone.require' => '手机号不能为空',
        'phone.mobile' => '手机号格式错误',
        'id_card.length' => '身份证号码长度必须为18位',
        'position.require' => '职位不能为空',
        'position.length' => '职位长度不能超过50个字符',
        'role_type.require' => '请选择角色类型',
        'role_type.in' => '角色类型值错误',
        'permissions.array' => '权限配置格式错误',
        'entry_date.require' => '请选择入职日期',
        'entry_date.date' => '入职日期格式错误',
        'salary_type.require' => '请选择薪资类型',
        'salary_type.in' => '薪资类型值错误',
        'base_salary.require' => '基础工资不能为空',
        'base_salary.egt' => '基础工资不能小于0',
        'commission_rate.egt' => '提成比例不能小于0',
        'commission_rate.elt' => '提成比例不能大于100',
        'status.in' => '状态值错误',
    ];

    protected $scene = [
        "add" => ['store_id', 'user_id', 'staff_code', 'real_name', 'phone', 'id_card', 'position', 'role_type', 'permissions', 'entry_date', 'salary_type', 'base_salary', 'commission_rate', 'status'],
        "edit" => ['store_id', 'user_id', 'staff_code', 'real_name', 'phone', 'id_card', 'position', 'role_type', 'permissions', 'entry_date', 'salary_type', 'base_salary', 'commission_rate', 'status'],
        "status" => ['status'],
    ];
}
