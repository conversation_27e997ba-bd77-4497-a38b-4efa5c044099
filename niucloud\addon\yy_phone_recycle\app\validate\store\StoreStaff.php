<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\validate\store;

use core\base\BaseValidate;

/**
 * 门店员工验证器
 * Class StoreStaff
 * @package addon\yy_phone_recycle\app\validate\store
 */
class StoreStaff extends BaseValidate
{
    protected $rule = [
        'member_id' => 'require|integer|gt:0',
        'real_name' => 'require|length:1,20',
        'phone' => 'require|mobile',
        'entry_date' => 'require|date',
    ];

    protected $message = [
        'member_id.require' => '会员ID不能为空',
        'member_id.integer' => '会员ID必须为整数',
        'member_id.gt' => '会员ID必须大于0',
        'real_name.require' => '员工姓名不能为空',
        'real_name.length' => '员工姓名长度不能超过20个字符',
        'phone.require' => '手机号不能为空',
        'phone.mobile' => '手机号格式不正确',
        'entry_date.require' => '入职日期不能为空',
        'entry_date.date' => '入职日期格式不正确',
    ];

    protected $scene = [
        'add' => ['member_id', 'real_name', 'phone', 'entry_date'],
        'edit' => ['member_id', 'real_name', 'phone', 'entry_date'],
    ];
}
