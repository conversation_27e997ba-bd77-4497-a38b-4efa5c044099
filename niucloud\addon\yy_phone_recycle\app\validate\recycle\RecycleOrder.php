<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\validate\recycle;

use core\base\BaseValidate;

/**
 * 回收订单验证器
 * Class RecycleOrder
 * @package addon\yy_phone_recycle\app\validate\recycle
 */
class RecycleOrder extends BaseValidate
{
    protected $rule = [
        'store_id' => 'require|gt:0',
        'customer_name' => 'require|length:1,50',
        'customer_phone' => 'require|mobile',
        'customer_id_card' => 'length:18',
        'phone_model_id' => 'require|gt:0',
        'phone_imei' => 'length:1,50',
        'phone_color' => 'length:1,50',
        'phone_storage' => 'length:1,50',
        'evaluation_template_id' => 'require|gt:0',
        'estimated_price' => 'require|egt:0',
        'payment_method' => 'in:1,2,3,4',
        'payment_account' => 'length:1,100',
        'adjustment_reason' => 'length:1,500',
        'remarks' => 'length:1,1000',
    ];

    protected $message = [
        'store_id.require' => '请选择门店',
        'store_id.gt' => '请选择门店',
        'customer_name.require' => '客户姓名不能为空',
        'customer_name.length' => '客户姓名长度不能超过50个字符',
        'customer_phone.require' => '客户手机号不能为空',
        'customer_phone.mobile' => '客户手机号格式错误',
        'customer_id_card.length' => '身份证号码长度必须为18位',
        'phone_model_id.require' => '请选择手机型号',
        'phone_model_id.gt' => '请选择手机型号',
        'phone_imei.length' => 'IMEI长度不能超过50个字符',
        'phone_color.length' => '手机颜色长度不能超过50个字符',
        'phone_storage.length' => '存储容量长度不能超过50个字符',
        'evaluation_template_id.require' => '请选择评估模板',
        'evaluation_template_id.gt' => '请选择评估模板',
        'estimated_price.require' => '预估价格不能为空',
        'estimated_price.egt' => '预估价格不能小于0',
        'payment_method.in' => '支付方式值错误',
        'payment_account.length' => '支付账户长度不能超过100个字符',
        'adjustment_reason.length' => '调价原因长度不能超过500个字符',
        'remarks.length' => '备注长度不能超过1000个字符',
    ];

    protected $scene = [
        "add" => ['store_id', 'customer_name', 'customer_phone', 'customer_id_card', 'phone_model_id', 'phone_imei', 'phone_color', 'phone_storage', 'evaluation_template_id', 'estimated_price', 'payment_method', 'payment_account', 'remarks'],
        "edit" => ['store_id', 'customer_name', 'customer_phone', 'customer_id_card', 'phone_model_id', 'phone_imei', 'phone_color', 'phone_storage', 'evaluation_template_id', 'estimated_price', 'payment_method', 'payment_account', 'remarks'],
        "complete" => ['final_price', 'payment_method', 'payment_account'],
        "cancel" => ['cancel_reason'],
    ];
}
