<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace app\model\member;

use core\base\BaseModel;

/**
 * 会员收货地址模型
 * Class MemberAddress
 * @package app\model\member
 */
class MemberAddress extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'member_address';

    /**
     * 搜索器:会员ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchMemberIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('member_id', '=', $value);
        }
    }

    /**
     * 搜索器:收货人姓名
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchNameAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器:手机号
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchMobileAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('mobile', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器:省份ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchProvinceIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('province_id', '=', $value);
        }
    }

    /**
     * 搜索器:城市ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchCityIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('city_id', '=', $value);
        }
    }

    /**
     * 搜索器:是否默认地址
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchIsDefaultAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('is_default', '=', $value);
        }
    }

    /**
     * 搜索器:完整地址
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchFullAddressAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('full_address', 'like', '%' . $value . '%');
        }
    }

    /**
     * 关联省份
     * @return \think\model\relation\HasOne
     */
    public function province()
    {
        return $this->hasOne('app\model\sys\SysArea', 'id', 'province_id')->field('id, name');
    }

    /**
     * 关联城市
     * @return \think\model\relation\HasOne
     */
    public function city()
    {
        return $this->hasOne('app\model\sys\SysArea', 'id', 'city_id')->field('id, name');
    }

    /**
     * 关联区县
     * @return \think\model\relation\HasOne
     */
    public function district()
    {
        return $this->hasOne('app\model\sys\SysArea', 'id', 'district_id')->field('id, name');
    }

    /**
     * 关联会员
     * @return \think\model\relation\HasOne
     */
    public function member()
    {
        return $this->hasOne('app\model\member\Member', 'member_id', 'member_id')->field('member_id, username, nickname, mobile');
    }

}
