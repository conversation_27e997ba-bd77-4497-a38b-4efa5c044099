<template>
    <div v-loading="loading">
        <el-descriptions :column="2" border>
            <el-descriptions-item label="员工编号">
                {{ staffInfo.staff_code }}
            </el-descriptions-item>
            <el-descriptions-item label="姓名">
                {{ staffInfo.real_name }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
                {{ staffInfo.phone }}
            </el-descriptions-item>
            <el-descriptions-item label="身份证号">
                {{ staffInfo.id_card || '未填写' }}
            </el-descriptions-item>
            <el-descriptions-item label="所属门店">
                {{ staffInfo.store?.store_name || '未分配' }}
            </el-descriptions-item>
            <el-descriptions-item label="职位">
                {{ staffInfo.position }}
            </el-descriptions-item>
            <el-descriptions-item label="角色类型">
                <el-tag :type="getRoleTypeTagType(staffInfo.role_type)">
                    {{ getRoleTypeName(staffInfo.role_type) }}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="入职日期">
                {{ staffInfo.entry_date }}
            </el-descriptions-item>
            <el-descriptions-item label="薪资类型">
                {{ getSalaryTypeName(staffInfo.salary_type) }}
            </el-descriptions-item>
            <el-descriptions-item label="基础工资">
                ¥{{ staffInfo.base_salary }}
            </el-descriptions-item>
            <el-descriptions-item label="提成比例" v-if="staffInfo.salary_type !== 1">
                {{ staffInfo.commission_rate }}%
            </el-descriptions-item>
            <el-descriptions-item label="状态">
                <el-tag :type="staffInfo.status === 1 ? 'success' : 'danger'">
                    {{ staffInfo.status === 1 ? '在职' : '离职' }}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
                {{ staffInfo.create_time }}
            </el-descriptions-item>
        </el-descriptions>

        <!-- 权限配置 -->
        <div class="mt-6" v-if="getPermissionArray(staffInfo.permissions).length > 0">
            <h4 class="mb-4">权限配置</h4>
            <el-card shadow="never">
                <div class="permissions-grid">
                    <el-tag
                        v-for="permission in getPermissionArray(staffInfo.permissions)"
                        :key="permission"
                        class="mr-2 mb-2"
                        type="info"
                    >
                        {{ getPermissionName(permission) }}
                    </el-tag>
                </div>
            </el-card>
        </div>

        <!-- 工作统计 -->
        <div class="mt-6" v-if="workStats">
            <h4 class="mb-4">工作统计</h4>
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-card shadow="never" class="stat-card">
                        <div class="stat-item">
                            <div class="stat-value">{{ workStats.total_orders || 0 }}</div>
                            <div class="stat-label">处理订单数</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card shadow="never" class="stat-card">
                        <div class="stat-item">
                            <div class="stat-value">¥{{ workStats.total_amount || 0 }}</div>
                            <div class="stat-label">处理金额</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card shadow="never" class="stat-card">
                        <div class="stat-item">
                            <div class="stat-value">{{ workStats.this_month_orders || 0 }}</div>
                            <div class="stat-label">本月订单</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card shadow="never" class="stat-card">
                        <div class="stat-item">
                            <div class="stat-value">¥{{ workStats.this_month_amount || 0 }}</div>
                            <div class="stat-label">本月金额</div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getStoreStaffInfo } from '@/addon/yy_phone_recycle/api/store'

interface Props {
    staffId: number
}

const props = defineProps<Props>()
const emit = defineEmits(['close'])

const loading = ref(false)
const staffInfo = ref<any>({})
const workStats = ref<any>(null)

/**
 * 加载员工详情
 */
const loadStaffInfo = async () => {
    loading.value = true
    try {
        const { data } = await getStoreStaffInfo(props.staffId)
        staffInfo.value = data
        workStats.value = data.work_stats || null
    } catch (error) {
        console.error('加载员工详情失败:', error)
        ElMessage.error('加载员工详情失败')
    } finally {
        loading.value = false
    }
}

/**
 * 获取角色类型名称
 */
const getRoleTypeName = (roleType: number) => {
    const roleMap = {
        1: '店长',
        2: '收银员',
        3: '评估师',
        4: '普通员工'
    }
    return roleMap[roleType] || '未知'
}

/**
 * 获取角色类型标签类型
 */
const getRoleTypeTagType = (roleType: number) => {
    const typeMap = {
        1: 'danger',
        2: 'warning',
        3: 'success',
        4: 'info'
    }
    return typeMap[roleType] || 'info'
}

/**
 * 获取薪资类型名称
 */
const getSalaryTypeName = (salaryType: number) => {
    const salaryMap = {
        1: '固定工资',
        2: '提成制',
        3: '混合制'
    }
    return salaryMap[salaryType] || '未知'
}

/**
 * 获取权限数组
 */
const getPermissionArray = (permissions: any) => {
    if (!permissions) return []
    if (Array.isArray(permissions)) return permissions
    if (typeof permissions === 'object' && permissions !== null) {
        // 如果是对象格式 {"0": "order_manage", "1": "customer_manage"}，转换为数组
        return Object.values(permissions)
    }
    return []
}

/**
 * 获取权限名称
 */
const getPermissionName = (permission: string) => {
    const permissionMap = {
        'order_manage': '订单管理',
        'evaluation_manage': '评估管理',
        'inventory_manage': '库存管理',
        'customer_manage': '客户管理',
        'finance_view': '财务查看',
        'report_view': '报表查看',
        'staff_manage': '员工管理',
        'store_setting': '门店设置'
    }
    return permissionMap[permission] || permission
}

onMounted(() => {
    loadStaffInfo()
})
</script>

<style lang="scss" scoped>
.permissions-grid {
    display: flex;
    flex-wrap: wrap;
}

.stat-card {
    text-align: center;
    
    .stat-item {
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
    }
}
</style>
