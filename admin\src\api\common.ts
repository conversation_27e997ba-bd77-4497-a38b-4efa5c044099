import request from '@/utils/request'

/**
 * 获取地区列表（根据父级ID）
 * @param params
 * @returns
 */
export function getAreaList(params: Record<string, any>) {
    return request.get(`sys/area/list_by_pid/${params.pid || 0}`)
}

/**
 * 获取地区树列表
 * @param level
 * @returns
 */
export function getAreaTree(level: number = 3) {
    return request.get(`sys/area/tree/${level}`)
}

/**
 * 获取地区信息
 * @param params
 * @returns
 */
export function getAreaInfo(params: Record<string, any>) {
    return request.get(`sys/area/get_info`, { params })
}
