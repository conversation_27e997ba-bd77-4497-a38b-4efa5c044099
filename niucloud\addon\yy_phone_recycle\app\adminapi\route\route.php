<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

use think\facade\Route;

use app\adminapi\middleware\AdminCheckRole;
use app\adminapi\middleware\AdminCheckToken;
use app\adminapi\middleware\AdminLog;

/**
 * 手机回收管理
 */
Route::group('yy_phone_recycle', function () {

    /***************************************************** 一级分类管理 ****************************************************/
    // 获取分类选项 (需要放在前面，避免路由冲突)
    Route::get('category/options', 'addon\yy_phone_recycle\app\adminapi\controller\Category@options');
    // 修改分类状态
    Route::put('category/status/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Category@modifyStatus');
    // 分类列表
    Route::get('category', 'addon\yy_phone_recycle\app\adminapi\controller\Category@lists');
    // 分类详情
    Route::get('category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Category@info');
    // 添加分类
    Route::post('category', 'addon\yy_phone_recycle\app\adminapi\controller\Category@add');
    // 编辑分类
    Route::put('category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Category@edit');
    // 删除分类
    Route::delete('category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Category@del');

    /***************************************************** 二级分类管理 ****************************************************/
    // 根据一级分类获取二级分类选项 (需要放在前面，避免路由冲突)
    Route::get('subcategory/options/:category_id', 'addon\yy_phone_recycle\app\adminapi\controller\Subcategory@optionsByCategoryId');
    // 修改分类状态
    Route::put('subcategory/status/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Subcategory@modifyStatus');
    // 分类列表
    Route::get('subcategory', 'addon\yy_phone_recycle\app\adminapi\controller\Subcategory@lists');
    // 分类详情
    Route::get('subcategory/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Subcategory@info');
    // 添加分类
    Route::post('subcategory', 'addon\yy_phone_recycle\app\adminapi\controller\Subcategory@add');
    // 编辑分类
    Route::put('subcategory/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Subcategory@edit');
    // 删除分类
    Route::delete('subcategory/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Subcategory@del');

    /***************************************************** 品牌管理 ****************************************************/
    // 获取品牌选项 (需要放在前面，避免路由冲突)
    Route::get('brand/options', 'addon\yy_phone_recycle\app\adminapi\controller\Brand@options');
    // 获取首字母列表
    Route::get('brand/letters', 'addon\yy_phone_recycle\app\adminapi\controller\Brand@letterList');
    // 修改品牌状态
    Route::put('brand/status/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Brand@modifyStatus');
    // 品牌列表
    Route::get('brand', 'addon\yy_phone_recycle\app\adminapi\controller\Brand@lists');
    // 品牌详情
    Route::get('brand/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Brand@info');
    // 添加品牌
    Route::post('brand', 'addon\yy_phone_recycle\app\adminapi\controller\Brand@add');
    // 编辑品牌
    Route::put('brand/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Brand@edit');
    // 删除品牌
    Route::delete('brand/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Brand@del');

    /***************************************************** 品牌分类关联管理 ****************************************************/
    // 获取品牌的分类关联
    Route::get('brand_category/brand/:brand_id', 'addon\yy_phone_recycle\app\adminapi\controller\BrandCategory@getBrandCategories');
    // 批量设置品牌分类关联
    Route::post('brand_category/batch', 'addon\yy_phone_recycle\app\adminapi\controller\BrandCategory@setBrandCategories');
    // 关联列表
    Route::get('brand_category', 'addon\yy_phone_recycle\app\adminapi\controller\BrandCategory@lists');
    // 关联详情
    Route::get('brand_category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\BrandCategory@info');
    // 添加关联
    Route::post('brand_category', 'addon\yy_phone_recycle\app\adminapi\controller\BrandCategory@add');
    // 编辑关联
    Route::put('brand_category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\BrandCategory@edit');
    // 删除关联
    Route::delete('brand_category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\BrandCategory@del');

    /***************************************************** 型号系列管理 ****************************************************/
    // 获取系列选项 (需要放在前面，避免路由冲突)
    Route::get('series/options', 'addon\yy_phone_recycle\app\adminapi\controller\Series@options');
    // 修改系列状态
    Route::put('series/status/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Series@modifyStatus');
    // 系列列表
    Route::get('series', 'addon\yy_phone_recycle\app\adminapi\controller\Series@lists');
    // 系列详情
    Route::get('series/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Series@info');
    // 添加系列
    Route::post('series', 'addon\yy_phone_recycle\app\adminapi\controller\Series@add');
    // 编辑系列
    Route::put('series/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Series@edit');
    // 删除系列
    Route::delete('series/:id', 'addon\yy_phone_recycle\app\adminapi\controller\Series@del');

    /***************************************************** 型号管理 ****************************************************/
    // 修改型号状态
    Route::put('phone_model/status/:id', 'addon\yy_phone_recycle\app\adminapi\controller\PhoneModel@modifyStatus');
    // 型号列表
    Route::get('phone_model', 'addon\yy_phone_recycle\app\adminapi\controller\PhoneModel@lists');
    // 型号详情
    Route::get('phone_model/:id', 'addon\yy_phone_recycle\app\adminapi\controller\PhoneModel@info');
    // 添加型号
    Route::post('phone_model', 'addon\yy_phone_recycle\app\adminapi\controller\PhoneModel@add');
    // 编辑型号
    Route::put('phone_model/:id', 'addon\yy_phone_recycle\app\adminapi\controller\PhoneModel@edit');
    // 删除型号
    Route::delete('phone_model/:id', 'addon\yy_phone_recycle\app\adminapi\controller\PhoneModel@del');

    /***************************************************** 估价模板管理 ****************************************************/
    // 获取手机型号列表 (用于模板创建)
    Route::get('evaluation/template/phone_models', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@getPhoneModelList');
    // 获取可复制的模板列表
    Route::get('evaluation/template/copyable', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@getCopyableTemplates');
    // 导入模板配置
    Route::post('evaluation/template/import', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@importTemplate');
    // 复制模板
    Route::post('evaluation/template/:id/copy', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@copy');
    // 修改模板状态
    Route::put('evaluation/template/:id/status', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@modifyStatus');
    // 获取模板统计信息
    Route::get('evaluation/template/:id/statistics', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@getStatistics');
    // 导出模板配置
    Route::get('evaluation/template/:id/export', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@exportTemplate');
    // 模板列表
    Route::get('evaluation/template', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@lists');
    // 模板详情
    Route::get('evaluation/template/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@info');
    // 添加模板
    Route::post('evaluation/template', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@add');
    // 编辑模板
    Route::put('evaluation/template/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@edit');
    // 删除模板
    Route::delete('evaluation/template/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationTemplate@del');

    /***************************************************** 题目分类管理 ****************************************************/
    // 获取分类选择器数据
    Route::get('evaluation/question_category/selector', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionCategory@getCategorySelector');
    // 批量排序
    Route::put('evaluation/question_category/batch_sort', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionCategory@batchSort');
    // 修改分类状态
    Route::put('evaluation/question_category/:id/status', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionCategory@modifyStatus');
    // 获取分类树形列表
    Route::get('evaluation/question_category/tree', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionCategory@tree');
    // 分类列表
    Route::get('evaluation/question_category', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionCategory@lists');
    // 分类详情
    Route::get('evaluation/question_category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionCategory@info');
    // 添加分类
    Route::post('evaluation/question_category', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionCategory@add');
    // 编辑分类
    Route::put('evaluation/question_category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionCategory@edit');
    // 删除分类
    Route::delete('evaluation/question_category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionCategory@del');

    /***************************************************** 题目库管理 ****************************************************/
    // 获取题目分类列表
    Route::get('evaluation/question_library/categories', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@getCategoryList');
    // 获取题目类型选项
    Route::get('evaluation/question_library/question_types', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@getQuestionTypes');
    // 获取布局类型选项
    Route::get('evaluation/question_library/layout_types', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@getLayoutTypes');
    // 批量导入题目
    Route::post('evaluation/question_library/batch_import', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@batchImport');
    // 导出题目数据
    Route::get('evaluation/question_library/export', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@exportQuestions');
    // 批量删除题目
    Route::delete('evaluation/question_library/batch', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@batchDelete');
    // 复制题目
    Route::post('evaluation/question_library/:id/copy', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@copy');
    // 修改题目状态
    Route::put('evaluation/question_library/:id/status', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@modifyStatus');
    // 题目列表
    Route::get('evaluation/question_library', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@lists');
    // 题目详情
    Route::get('evaluation/question_library/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@info');
    // 添加题目
    Route::post('evaluation/question_library', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@add');
    // 编辑题目
    Route::put('evaluation/question_library/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@edit');
    // 删除题目
    Route::delete('evaluation/question_library/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\QuestionLibrary@del');

    /***************************************************** 选项分类管理 ****************************************************/
    // 获取分类选择器数据
    Route::get('evaluation/option_category/selector', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionCategory@getCategorySelector');
    // 批量排序
    Route::put('evaluation/option_category/batch_sort', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionCategory@batchSort');
    // 修改分类状态
    Route::put('evaluation/option_category/:id/status', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionCategory@modifyStatus');
    // 获取分类树形列表
    Route::get('evaluation/option_category/tree', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionCategory@tree');
    // 分类列表
    Route::get('evaluation/option_category', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionCategory@lists');
    // 分类详情
    Route::get('evaluation/option_category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionCategory@info');
    // 添加分类
    Route::post('evaluation/option_category', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionCategory@add');
    // 编辑分类
    Route::put('evaluation/option_category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionCategory@edit');
    // 删除分类
    Route::delete('evaluation/option_category/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionCategory@del');

    /***************************************************** 选项库管理 ****************************************************/
    // 获取选项分类列表
    Route::get('evaluation/option_library/categories', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@getCategoryList');
    // 获取图标类型选项
    Route::get('evaluation/option_library/icon_types', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@getIconTypes');
    // 获取减价类型选项
    Route::get('evaluation/option_library/deduction_types', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@getDeductionTypes');
    // 获取颜色方案选项
    Route::get('evaluation/option_library/color_schemes', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@getColorSchemes');
    // 批量导入选项
    Route::post('evaluation/option_library/batch_import', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@batchImport');
    // 导出选项数据
    Route::get('evaluation/option_library/export', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@exportOptions');
    // 批量删除选项
    Route::delete('evaluation/option_library/batch', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@batchDelete');
    // 复制选项
    Route::post('evaluation/option_library/:id/copy', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@copy');
    // 修改选项状态
    Route::put('evaluation/option_library/:id/status', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@modifyStatus');
    // 选项列表
    Route::get('evaluation/option_library', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@lists');
    // 选项详情
    Route::get('evaluation/option_library/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@info');
    // 添加选项
    Route::post('evaluation/option_library', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@add');
    // 编辑选项
    Route::put('evaluation/option_library/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@edit');
    // 删除选项
    Route::delete('evaluation/option_library/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\OptionLibrary@del');

    /***************************************************** 流程设计器 ****************************************************/
    // 获取已保存流程列表
    Route::get('evaluation/flow_designer/saved_flows', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@getSavedFlowsList');
    // 删除流程配置
    Route::delete('evaluation/flow_designer/:template_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@deleteFlow');
    // 验证流程配置
    Route::get('evaluation/flow_designer/:template_id/validate', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@validateFlow');
    // 复制流程配置
    Route::post('evaluation/flow_designer/:source_template_id/copy/:target_template_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@copyFlow');
    // 预览流程
    Route::get('evaluation/flow_designer/:template_id/preview', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@previewFlow');
    // 获取可用选项库列表
    Route::get('evaluation/flow_designer/available_options/:template_question_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@getAvailableOptions');
    // 获取可用题目库列表
    Route::get('evaluation/flow_designer/:template_id/available_questions', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@getAvailableQuestions');
    // 移除题目中的选项
    Route::delete('evaluation/flow_designer/option/:template_option_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@removeOptionFromQuestion');
    // 移除流程中的题目
    Route::delete('evaluation/flow_designer/question/:template_question_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@removeQuestionFromFlow');
    // 添加选项到题目
    Route::post('evaluation/flow_designer/add_option', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@addOptionToQuestion');
    // 批量添加选项到题目
    Route::post('evaluation/flow_designer/batch_add_options', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@batchAddOptionsToQuestion');
    // 添加题目到流程
    Route::post('evaluation/flow_designer/:template_id/add_question', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@addQuestionToFlow');
    // 保存流程设计数据
    Route::put('evaluation/flow_designer/:template_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@saveFlowData');
    // 获取模板流程设计数据
    Route::get('evaluation/flow_designer/:template_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@getFlowData');
    // 获取可用的题目库列表
    Route::get('evaluation/flow_designer/available_questions/:template_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@getAvailableQuestions');
    // 获取可用的选项库列表
    Route::get('evaluation/flow_designer/available_options/:template_question_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@getAvailableOptions');

    // 更新选项配置
    Route::put('evaluation/flow_designer/option/:template_option_id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@updateOptionConfig');
    // 批量更新选项配置
    Route::put('evaluation/flow_designer/options/batch', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@batchUpdateOptionConfig');
    // 获取题目分类列表
    Route::get('evaluation/flow_designer/question_categories', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@getQuestionCategories');
    // 获取选项分类列表
    Route::get('evaluation/flow_designer/option_categories', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\FlowDesigner@getOptionCategories');

    // ==================== 估价记录管理 ====================
    // 获取估价记录列表
    Route::get('evaluation/record', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationRecord@lists');
    // 获取估价记录详情
    Route::get('evaluation/record/:id', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationRecord@info');
    // 删除估价记录
    Route::delete('evaluation/record', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationRecord@del');
    // 删除估价记录（POST方式，用于批量删除）
    Route::post('evaluation/record/delete', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationRecord@del');
    // 获取估价记录统计
    Route::get('evaluation/record/statistics', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationRecord@statistics');
    // 获取趋势数据
    Route::get('evaluation/record/trend', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationRecord@trend');
    // 获取来源分布
    Route::get('evaluation/record/source_distribution', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationRecord@sourceDistribution');
    // 获取热门型号排行
    Route::get('evaluation/record/popular_models', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationRecord@popularModels');
    // 导出估价记录
    Route::post('evaluation/record/export', 'addon\yy_phone_recycle\app\adminapi\controller\evaluation\EvaluationRecord@export');

    /***************************************************** 门店管理 ****************************************************/
    // 获取门店选项列表 (需要放在前面，避免路由冲突)
    Route::get('store/options', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@options');

    // 门店基础管理路由
    Route::get('store', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@lists');
    Route::get('store/:id', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@info');
    Route::post('store', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@add');
    Route::put('store/:id', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@edit');
    Route::delete('store/:id', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@del');
    Route::put('store/:id/status', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@modifyStatus');
    Route::get('store/:id/statistics', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@getStatistics');

    // 门店员工管理路由
    Route::get('store/:id/staff', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@staff');
    Route::post('store/:id/staff', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@addStaff');
    Route::put('store/:id/staff/:staff_id', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@editStaff');
    Route::delete('store/:id/staff/:staff_id', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@delStaff');
    Route::put('store/:id/staff/:staff_id/status', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@modifyStaffStatus');

    // 回收订单管理路由
    Route::get('recycle/order', 'addon\yy_phone_recycle\app\adminapi\controller\recycle\RecycleOrder@lists');
    Route::get('recycle/order/:id', 'addon\yy_phone_recycle\app\adminapi\controller\recycle\RecycleOrder@info');
    Route::post('recycle/order', 'addon\yy_phone_recycle\app\adminapi\controller\recycle\RecycleOrder@add');
    Route::put('recycle/order/:id', 'addon\yy_phone_recycle\app\adminapi\controller\recycle\RecycleOrder@edit');
    Route::delete('recycle/order/:id', 'addon\yy_phone_recycle\app\adminapi\controller\recycle\RecycleOrder@del');
    Route::put('recycle/order/:id/cancel', 'addon\yy_phone_recycle\app\adminapi\controller\recycle\RecycleOrder@cancel');
    Route::put('recycle/order/:id/complete', 'addon\yy_phone_recycle\app\adminapi\controller\recycle\RecycleOrder@complete');
    Route::put('recycle/order/:id/status', 'addon\yy_phone_recycle\app\adminapi\controller\recycle\RecycleOrder@modifyStatus');

    // 手机品牌管理路由
    Route::get('phone/brand', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneBrand@lists');
    Route::get('phone/brand/options', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneBrand@options');
    Route::get('phone/brand/:id', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneBrand@info');
    Route::post('phone/brand', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneBrand@add');
    Route::put('phone/brand/:id', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneBrand@edit');
    Route::delete('phone/brand/:id', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneBrand@del');
    Route::put('phone/brand/:id/status', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneBrand@modifyStatus');

    // 手机型号管理路由
    Route::get('phone/model', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneModel@lists');
    Route::get('phone/model/options', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneModel@options');
    Route::get('phone/model/:id', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneModel@info');
    Route::post('phone/model', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneModel@add');
    Route::put('phone/model/:id', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneModel@edit');
    Route::delete('phone/model/:id', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneModel@del');
    Route::put('phone/model/:id/status', 'addon\yy_phone_recycle\app\adminapi\controller\phone\PhoneModel@modifyStatus');


    // 门店列表
    Route::get('store', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@index');
    // 门店详情
    Route::get('store/:id', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@read');
    // 添加门店
    Route::post('store', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@add');
    // 编辑门店
    Route::put('store/:id', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@update');
    // 删除门店
    Route::delete('store/:id', 'addon\yy_phone_recycle\app\adminapi\controller\store\Store@delete');

})->middleware([
    AdminCheckToken::class,
    AdminCheckRole::class,
    AdminLog::class
]);