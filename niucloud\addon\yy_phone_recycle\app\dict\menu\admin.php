<?php

return  [
    [
        'menu_name' => '手机回收管理',
        'menu_key' => 'yy_phone_recycle',
        'menu_type' => 0,
        'icon' => 'element-Phone',
        'api_url' => '',
        'router_path' => '',
        'view_path' => '',
        'methods' => '',
        'sort' => 100,
        'status' => 1,
        'is_show' => 1,
        'children' => [
            // 回收订单管理
            [
                'menu_name' => '回收订单',
                'menu_key' => 'yy_phone_recycle_order_manage',
                'menu_type' => 0,
                'icon' => 'element-List',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 900,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '门店回收订单',
                        'menu_key' => 'yy_phone_recycle_order',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/recycle/order',
                        'router_path' => 'yy_phone_recycle/recycle/order',
                        'view_path' => 'recycle/order/index',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ]
                ]
            ],
            // 基础数据管理
            [
                'menu_name' => '基础数据',
                'menu_key' => 'yy_phone_recycle_basic_data',
                'menu_type' => 0,
                'icon' => 'element-DataBoard',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 800,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    // 分类管理
                    [
                        'menu_name' => '分类管理',
                        'menu_key' => 'yy_phone_recycle_category_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/category',
                        'router_path' => 'yy_phone_recycle/category',
                        'view_path' => 'category/index',
                        'methods' => 'get',
                        'sort' => 400,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    // 品牌管理
                    [
                        'menu_name' => '品牌管理',
                        'menu_key' => 'yy_phone_recycle_brand_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/phone/brand',
                        'router_path' => 'yy_phone_recycle/brand',
                        'view_path' => 'brand/index',
                        'methods' => 'get',
                        'sort' => 300,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    // 型号系列
                    [
                        'menu_name' => '型号系列',
                        'menu_key' => 'yy_phone_recycle_series_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/series',
                        'router_path' => 'yy_phone_recycle/series',
                        'view_path' => 'series/index',
                        'methods' => 'get',
                        'sort' => 200,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    // 型号管理
                    [
                        'menu_name' => '型号管理',
                        'menu_key' => 'yy_phone_recycle_model_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/phone/model',
                        'router_path' => 'yy_phone_recycle/phone_model',
                        'view_path' => 'phone_model/index',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ]
                ]
            ],
            // 估价管理
            [
                'menu_name' => '估价管理',
                'menu_key' => 'yy_phone_recycle_evaluation',
                'menu_type' => 0,
                'icon' => 'element-Money',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 700,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    // 估价模板管理
                    [
                        'menu_name' => '估价模板',
                        'menu_key' => 'yy_phone_recycle_evaluation_template',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/evaluation/template',
                        'router_path' => 'yy_phone_recycle/evaluation/template',
                        'view_path' => 'evaluation/template/index',
                        'methods' => 'get',
                        'sort' => 300,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    // 流程设计器
                    [
                        'menu_name' => '流程设计器',
                        'menu_key' => 'yy_phone_recycle_evaluation_flow_designer',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/evaluation/flow_designer',
                        'router_path' => 'yy_phone_recycle/evaluation/flow_designer',
                        'view_path' => 'evaluation/flow_designer/index',
                        'methods' => 'get',
                        'sort' => 200,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    // 估价记录
                    [
                        'menu_name' => '估价记录',
                        'menu_key' => 'yy_phone_recycle_evaluation_record',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/evaluation/record',
                        'router_path' => 'yy_phone_recycle/evaluation/record',
                        'view_path' => 'evaluation/record/index',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ]
                ]
            ],
            // 题目管理
            [
                'menu_name' => '题目管理',
                'menu_key' => 'yy_phone_recycle_question_manage',
                'menu_type' => 0,
                'icon' => 'element-Document',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 600,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    // 题目分类
                    [
                        'menu_name' => '题目分类',
                        'menu_key' => 'yy_phone_recycle_question_category',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/evaluation/question_category',
                        'router_path' => 'yy_phone_recycle/evaluation/question_category',
                        'view_path' => 'evaluation/question_category/index',
                        'methods' => 'get',
                        'sort' => 200,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    // 题目库
                    [
                        'menu_name' => '题目库',
                        'menu_key' => 'yy_phone_recycle_question_library',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/evaluation/question_library',
                        'router_path' => 'yy_phone_recycle/evaluation/question_library',
                        'view_path' => 'evaluation/question_library/index',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ]
                ]
            ],
            // 选项管理
            [
                'menu_name' => '选项管理',
                'menu_key' => 'yy_phone_recycle_option_manage',
                'menu_type' => 0,
                'icon' => 'element-Menu',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 500,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    // 选项分类
                    [
                        'menu_name' => '选项分类',
                        'menu_key' => 'yy_phone_recycle_option_category',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/evaluation/option_category',
                        'router_path' => 'yy_phone_recycle/evaluation/option_category',
                        'view_path' => 'evaluation/option_category/index',
                        'methods' => 'get',
                        'sort' => 200,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    // 选项库
                    [
                        'menu_name' => '选项库',
                        'menu_key' => 'yy_phone_recycle_option_library',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yy_phone_recycle/evaluation/option_library',
                        'router_path' => 'yy_phone_recycle/evaluation/option_library',
                        'view_path' => 'evaluation/option_library/index',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ]
                ]
            ],
            // 门店管理
            [
                'menu_name' => '门店管理',
                'menu_key' => 'yy_phone_recycle_store_manage',
                'menu_type' => 0,
                'icon' => 'element-Shop',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 400,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '门店信息',
                        'menu_key' => 'yy_phone_recycle_store_info',
                        'menu_type' => 1,
                        'icon' => 'element-Shop',
                        'api_url' => 'yy_phone_recycle/store',
                        'router_path' => 'yy_phone_recycle/store',
                        'view_path' => 'store/index',
                        'methods' => 'get',
                        'sort' => 401,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '员工管理',
                        'menu_key' => 'yy_phone_recycle_store_staff',
                        'menu_type' => 1,
                        'icon' => 'element-User',
                        'api_url' => 'yy_phone_recycle/store/staff',
                        'router_path' => 'yy_phone_recycle/store/staff',
                        'view_path' => 'store/staff/index',
                        'methods' => 'get',
                        'sort' => 402,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ]
                ]
            ],
            // 地址管理
            [
                'menu_name' => '地址管理',
                'menu_key' => 'yy_phone_recycle_member_address',
                'menu_type' => 1,
                'icon' => 'element-Location',
                'api_url' => 'yy_phone_recycle/member/address',
                'router_path' => 'yy_phone_recycle/member/address',
                'view_path' => 'member/address/index',
                'methods' => 'get',
                'sort' => 300,
                'status' => 1,
                'is_show' => 1,
                'children' => []
            ]
        ]
    ]
];
