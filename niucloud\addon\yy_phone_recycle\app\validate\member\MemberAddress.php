<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\validate\member;

use core\base\BaseValidate;

/**
 * 会员地址验证器
 * Class MemberAddress
 * @package addon\yy_phone_recycle\app\validate\member
 */
class MemberAddress extends BaseValidate
{
    protected $rule = [
        'member_id' => 'require|gt:0',
        'name' => 'require|length:1,50',
        'mobile' => 'require|mobile',
        'province_id' => 'require|gt:0',
        'city_id' => 'require|gt:0',
        'district_id' => 'require|gt:0',
        'address' => 'require|length:1,255',
        'address_name' => 'length:0,255',
        'lng' => 'length:0,50',
        'lat' => 'length:0,50',
        'is_default' => 'in:0,1',
    ];

    protected $message = [
        'member_id.require' => '请选择会员',
        'member_id.gt' => '请选择会员',
        'name.require' => '收货人姓名不能为空',
        'name.length' => '收货人姓名长度不能超过50个字符',
        'mobile.require' => '手机号不能为空',
        'mobile.mobile' => '手机号格式错误',
        'province_id.require' => '请选择省份',
        'province_id.gt' => '请选择省份',
        'city_id.require' => '请选择城市',
        'city_id.gt' => '请选择城市',
        'district_id.require' => '请选择区县',
        'district_id.gt' => '请选择区县',
        'address.require' => '详细地址不能为空',
        'address.length' => '详细地址长度不能超过255个字符',
        'address_name.length' => '地址标签长度不能超过255个字符',
        'lng.length' => '经度长度不能超过50个字符',
        'lat.length' => '纬度长度不能超过50个字符',
        'is_default.in' => '默认地址值错误',
    ];

    protected $scene = [
        "add" => ['member_id', 'name', 'mobile', 'province_id', 'city_id', 'district_id', 'address', 'address_name', 'lng', 'lat', 'is_default'],
        "edit" => ['member_id', 'name', 'mobile', 'province_id', 'city_id', 'district_id', 'address', 'address_name', 'lng', 'lat', 'is_default'],
    ];
}
