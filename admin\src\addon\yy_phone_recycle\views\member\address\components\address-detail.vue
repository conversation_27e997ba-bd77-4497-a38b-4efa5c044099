<template>
    <div v-loading="loading">
        <el-descriptions :column="2" border>
            <el-descriptions-item label="地址ID">
                {{ addressInfo.id }}
            </el-descriptions-item>
            <el-descriptions-item label="会员信息">
                <div>{{ addressInfo.member?.nickname || addressInfo.member?.username || '未知' }}</div>
                <div class="text-xs text-gray-500">{{ addressInfo.member?.mobile }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="收货人">
                {{ addressInfo.name }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
                {{ addressInfo.mobile }}
            </el-descriptions-item>
            <el-descriptions-item label="省份">
                {{ addressInfo.province?.name || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="城市">
                {{ addressInfo.city?.name || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="区县">
                {{ addressInfo.district?.name || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="地址标签">
                <el-tag v-if="addressInfo.address_name" size="small" type="info">
                    {{ addressInfo.address_name }}
                </el-tag>
                <span v-else class="text-gray-400">无</span>
            </el-descriptions-item>
            <el-descriptions-item label="详细地址" :span="2">
                {{ addressInfo.address }}
            </el-descriptions-item>
            <el-descriptions-item label="完整地址" :span="2">
                {{ addressInfo.full_address }}
            </el-descriptions-item>
            <el-descriptions-item label="经度" v-if="addressInfo.lng">
                {{ addressInfo.lng }}
            </el-descriptions-item>
            <el-descriptions-item label="纬度" v-if="addressInfo.lat">
                {{ addressInfo.lat }}
            </el-descriptions-item>
            <el-descriptions-item label="默认地址">
                <el-tag :type="addressInfo.is_default === 1 ? 'success' : 'info'" size="small">
                    {{ addressInfo.is_default === 1 ? '默认地址' : '普通地址' }}
                </el-tag>
            </el-descriptions-item>
        </el-descriptions>

        <!-- 地图显示 -->
        <div class="mt-6" v-if="addressInfo.lng && addressInfo.lat">
            <h4 class="mb-4">地址位置</h4>
            <el-card shadow="never">
                <div class="map-container">
                    <div class="text-center text-gray-500">
                        <icon name="element-Location" size="48" />
                        <div class="mt-2">经度: {{ addressInfo.lng }}</div>
                        <div>纬度: {{ addressInfo.lat }}</div>
                        <div class="text-sm mt-2">{{ addressInfo.full_address }}</div>
                    </div>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getMemberAddressInfo } from '@/addon/yy_phone_recycle/api/member'

interface Props {
    addressData: any
}

const props = defineProps<Props>()
const emit = defineEmits(['close'])

const loading = ref(false)
const addressInfo = ref<any>({})

/**
 * 加载地址详情
 */
const loadAddressInfo = async () => {
    if (!props.addressData?.id) {
        addressInfo.value = props.addressData || {}
        return
    }

    loading.value = true
    try {
        const { data } = await getMemberAddressInfo(props.addressData.id)
        addressInfo.value = data
    } catch (error) {
        console.error('加载地址详情失败:', error)
        ElMessage.error('加载地址详情失败')
        // 如果加载失败，使用传入的数据
        addressInfo.value = props.addressData || {}
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    loadAddressInfo()
})
</script>

<style lang="scss" scoped>
.map-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border-radius: 4px;
}
</style>
