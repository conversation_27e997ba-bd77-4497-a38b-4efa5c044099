<template>
    <div class="store-edit-container">
        <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            v-loading="loading"
        >
            <!-- 基本信息 -->
            <div class="form-section">
                <h3 class="section-title">基本信息</h3>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="门店名称" prop="store_name">
                            <el-input v-model="formData.store_name" placeholder="请输入门店名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="门店编码" prop="store_code">
                            <el-input 
                                v-model="formData.store_code" 
                                placeholder="留空自动生成"
                                :disabled="!!storeId"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="门店类型" prop="store_type">
                            <el-select v-model="formData.store_type" placeholder="请选择门店类型">
                                <el-option label="直营店" :value="1" />
                                <el-option label="加盟店" :value="2" />
                                <el-option label="合作店" :value="3" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="状态" prop="status">
                            <el-radio-group v-model="formData.status">
                                <el-radio :label="1">正常营业</el-radio>
                                <el-radio :label="0">暂停营业</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <h3 class="section-title">联系信息</h3>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="联系人" prop="contact_name">
                            <el-input v-model="formData.contact_name" placeholder="请输入联系人姓名" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话" prop="contact_phone">
                            <el-input v-model="formData.contact_phone" placeholder="请输入联系电话" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 地址信息 -->
            <div class="form-section">
                <h3 class="section-title">地址信息</h3>
                <el-form-item label="所在地区" prop="province">
                    <select-area
                        :init-data="areaData"
                        @area-change="handleAreaChange"
                    />
                </el-form-item>

                <el-form-item label="详细地址" prop="address">
                    <el-input 
                        v-model="formData.address" 
                        placeholder="请输入详细地址"
                        type="textarea"
                        :rows="2"
                    />
                </el-form-item>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="经度" prop="longitude">
                            <el-input-number 
                                v-model="formData.longitude" 
                                placeholder="请输入经度"
                                :precision="6"
                                :step="0.000001"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="纬度" prop="latitude">
                            <el-input-number 
                                v-model="formData.latitude" 
                                placeholder="请输入纬度"
                                :precision="6"
                                :step="0.000001"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 营业时间 -->
            <div class="form-section">
                <h3 class="section-title">营业时间</h3>
                <div class="business-hours-form">
                    <div v-for="(day, key) in businessHours" :key="key" class="time-row">
                        <div class="day-label">{{ day.label }}：</div>
                        <el-checkbox v-model="day.enabled" @change="handleDayToggle(key, day.enabled)">
                            营业
                        </el-checkbox>
                        <template v-if="day.enabled">
                            <el-time-picker
                                v-model="day.start"
                                placeholder="开始时间"
                                format="HH:mm"
                                value-format="HH:mm"
                                style="width: 120px; margin-left: 10px"
                            />
                            <span style="margin: 0 10px">至</span>
                            <el-time-picker
                                v-model="day.end"
                                placeholder="结束时间"
                                format="HH:mm"
                                value-format="HH:mm"
                                style="width: 120px"
                            />
                        </template>
                    </div>
                </div>
            </div>

            <!-- 门店图片 -->
            <div class="form-section">
                <h3 class="section-title">门店图片</h3>
                <el-form-item label="门店图片" prop="store_images">
                    <upload-image
                        v-model="formData.store_images"
                        :limit="5"
                    />
                    <div class="form-tip">最多上传5张图片，建议尺寸：800x600</div>
                </el-form-item>
            </div>

            <!-- 其他信息 -->
            <div class="form-section">
                <h3 class="section-title">其他信息</h3>
                <el-form-item label="营业执照" prop="business_license">
                    <upload-image
                        v-model="formData.business_license"
                        :limit="1"
                    />
                </el-form-item>

                <el-form-item label="门店描述" prop="description">
                    <el-input 
                        v-model="formData.description" 
                        placeholder="请输入门店描述"
                        type="textarea"
                        :rows="4"
                        maxlength="500"
                        show-word-limit
                    />
                </el-form-item>

                <el-form-item label="排序" prop="sort">
                    <el-input-number 
                        v-model="formData.sort" 
                        :min="0"
                        :max="9999"
                        placeholder="数值越大越靠前"
                        style="width: 200px"
                    />
                </el-form-item>
            </div>
        </el-form>

        <div class="form-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
                {{ storeId ? '更新' : '创建' }}
            </el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getStoreInfo, addStore, editStore } from '@/addon/yy_phone_recycle/api/store'
import SelectArea from '@/components/select-area/index.vue'
import UploadImage from '@/components/upload-image/index.vue'

interface Props {
    storeId?: number
}

const props = defineProps<Props>()
const emit = defineEmits(['complete'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

// 表单数据
const formData = reactive({
    store_name: '',
    store_code: '',
    store_type: 1,
    manager_id: 0,
    contact_name: '',
    contact_phone: '',
    province: '',
    city: '',
    district: '',
    address: '',
    longitude: 0,
    latitude: 0,
    business_license: '',
    store_images: '',
    description: '',
    status: 1,
    sort: 0
})

// 地址数据 - 由于数据库只存储名称，这里暂时返回空，让用户重新选择
const areaData = computed(() => ({
    province: '',
    city: '',
    area: ''
}))

// 营业时间数据
const businessHours = reactive({
    monday: { label: '周一', enabled: true, start: '09:00', end: '18:00' },
    tuesday: { label: '周二', enabled: true, start: '09:00', end: '18:00' },
    wednesday: { label: '周三', enabled: true, start: '09:00', end: '18:00' },
    thursday: { label: '周四', enabled: true, start: '09:00', end: '18:00' },
    friday: { label: '周五', enabled: true, start: '09:00', end: '18:00' },
    saturday: { label: '周六', enabled: true, start: '09:00', end: '18:00' },
    sunday: { label: '周日', enabled: false, start: '09:00', end: '18:00' }
})

// 表单验证规则
const formRules = {
    store_name: [
        { required: true, message: '请输入门店名称', trigger: 'blur' },
        { min: 1, max: 100, message: '门店名称长度在1到100个字符', trigger: 'blur' }
    ],
    store_type: [
        { required: true, message: '请选择门店类型', trigger: 'change' }
    ],
    contact_name: [
        { required: true, message: '请输入联系人姓名', trigger: 'blur' },
        { min: 1, max: 50, message: '联系人姓名长度在1到50个字符', trigger: 'blur' }
    ],
    contact_phone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    province: [
        { required: true, message: '请选择省份', trigger: 'change' }
    ],

    address: [
        { required: true, message: '请输入详细地址', trigger: 'blur' },
        { min: 1, max: 200, message: '详细地址长度在1到200个字符', trigger: 'blur' }
    ]
}

/**
 * 加载门店信息
 */
const loadStoreInfo = async () => {
    if (!props.storeId) return
    
    loading.value = true
    try {
        const res = await getStoreInfo(props.storeId)
        const data = res.data
        
        // 填充表单数据
        Object.keys(formData).forEach(key => {
            if (data[key] !== undefined) {
                if (key === 'store_images' || key === 'business_license') {
                    // 处理图片数据：数组转换为逗号分隔的字符串
                    if (Array.isArray(data[key])) {
                        formData[key] = data[key].join(',')
                    } else {
                        formData[key] = data[key] || ''
                    }
                } else {
                    formData[key] = data[key]
                }
            }
        })
        
        // 处理营业时间
        if (data.business_hours) {
            let businessHoursData = data.business_hours

            // 如果是字符串，尝试解析JSON
            if (typeof businessHoursData === 'string') {
                try {
                    businessHoursData = JSON.parse(businessHoursData)
                } catch (error) {
                    console.warn('营业时间数据解析失败:', error)
                    businessHoursData = {}
                }
            }

            // 更新营业时间数据
            if (typeof businessHoursData === 'object') {
                Object.keys(businessHours).forEach(day => {
                    if (businessHoursData[day]) {
                        businessHours[day].enabled = businessHoursData[day].enabled === true
                        businessHours[day].start = businessHoursData[day].start || '09:00'
                        businessHours[day].end = businessHoursData[day].end || '18:00'
                    } else {
                        businessHours[day].enabled = false
                    }
                })
            }
        }
    } catch (error) {
        console.error('加载门店信息失败:', error)
        ElMessage.error('加载门店信息失败')
    } finally {
        loading.value = false
    }
}

/**
 * 处理营业日切换
 */
const handleDayToggle = (day: string, enabled: boolean) => {
    if (!enabled) {
        businessHours[day].start = '09:00'
        businessHours[day].end = '18:00'
    }
}

/**
 * 获取营业时间数据
 */
const getBusinessHoursData = () => {
    const result = {}
    Object.keys(businessHours).forEach(day => {
        result[day] = {
            enabled: businessHours[day].enabled,
            start: businessHours[day].start,
            end: businessHours[day].end
        }
    })
    return result
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
    try {
        await formRef.value.validate()
        
        submitting.value = true
        
        const submitData = {
            ...formData,
            business_hours: getBusinessHoursData()
        }
        
        if (props.storeId) {
            await editStore(props.storeId, submitData)
        } else {
            await addStore(submitData)
        }
        
        emit('complete')
    } catch (error) {
        console.error('提交失败:', error)
    } finally {
        submitting.value = false
    }
}

/**
 * 地址变化处理
 */
const handleAreaChange = (areaInfo: any) => {
    console.log('地址选择变化:', areaInfo)
    formData.province = areaInfo.province?.name || ''
    formData.city = areaInfo.city?.name || ''
    formData.district = areaInfo.area?.name || ''

    console.log('更新后的地址:', {
        province: formData.province,
        city: formData.city,
        district: formData.district
    })

    // 清除验证错误
    if (formRef.value) {
        formRef.value.clearValidate('province')
    }
}

/**
 * 取消操作
 */
const handleCancel = () => {
    emit('complete')
}

onMounted(() => {
    if (props.storeId) {
        loadStoreInfo()
    }
})
</script>

<style lang="scss" scoped>
.store-edit-container {
    .form-section {
        margin-bottom: 30px;
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #303133;
            border-left: 4px solid #409eff;
            padding-left: 12px;
        }
    }
    
    .business-hours-form {
        .time-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            
            .day-label {
                width: 60px;
                font-weight: 500;
                color: #606266;
            }
        }
    }
    
    .form-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
    }
    
    .form-footer {
        text-align: right;
        padding-top: 20px;
        border-top: 1px solid #ebeef5;
        
        .el-button {
            margin-left: 12px;
        }
    }
}
</style>
