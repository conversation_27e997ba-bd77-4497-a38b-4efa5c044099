<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\service\admin\member;

use app\model\member\MemberAddress;
use app\model\member\Member;
use core\base\BaseAdminService;

/**
 * 会员地址服务层
 * Class MemberAddressService
 * @package addon\yy_phone_recycle\app\service\admin\member
 */
class MemberAddressService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new MemberAddress();
    }

    /**
     * 获取会员地址列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id, member_id, name, mobile, province_id, city_id, district_id, address, address_name, full_address, is_default';
        $order = 'is_default desc, id desc';

        $search_model = $this->model
            ->withSearch(["member_id", "name", "mobile", "province_id", "city_id", "is_default", "full_address"], $where)
            ->with([
                'province' => function($query) {
                    $query->field('id, name');
                },
                'city' => function($query) {
                    $query->field('id, name');
                },
                'district' => function($query) {
                    $query->field('id, name');
                },
                'member' => function($query) {
                    $query->field('member_id, username, nickname, mobile');
                }
            ])
            ->field($field)
            ->order($order);

        return $this->pageQuery($search_model);
    }

    /**
     * 获取会员地址信息
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = 'id, member_id, name, mobile, province_id, city_id, district_id, address, address_name, full_address, lng, lat, is_default';

        $info = $this->model->field($field)
            ->with([
                'province' => function($query) {
                    $query->field('id, name');
                },
                'city' => function($query) {
                    $query->field('id, name');
                },
                'district' => function($query) {
                    $query->field('id, name');
                },
                'member' => function($query) {
                    $query->field('member_id, username, nickname, mobile');
                }
            ])
            ->where([['id', '=', $id]])
            ->findOrEmpty();

        return $info->toArray();
    }

    /**
     * 添加会员地址
     * @param array $data
     * @return mixed
     */
    public function add(array $data)
    {
        // 如果设置为默认地址，先取消其他默认地址
        if ($data['is_default'] == 1) {
            $this->model->where('member_id', $data['member_id'])->update(['is_default' => 0]);
        }

        // 生成完整地址
        $data['full_address'] = $this->generateFullAddress($data);

        $res = $this->model->create($data);
        return $res->id;
    }

    /**
     * 编辑会员地址
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function edit(int $id, array $data)
    {
        $address = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($address->isEmpty()) {
            throw new \Exception('地址不存在');
        }

        // 如果设置为默认地址，先取消其他默认地址
        if ($data['is_default'] == 1) {
            $this->model->where('member_id', $address['member_id'])->where('id', '<>', $id)->update(['is_default' => 0]);
        }

        // 生成完整地址
        $data['full_address'] = $this->generateFullAddress($data);

        $res = $this->model->where([['id', '=', $id]])->update($data);
        return true;
    }

    /**
     * 删除会员地址
     * @param int $id
     * @return bool
     */
    public function del(int $id)
    {
        $res = $this->model->where([['id', '=', $id]])->delete();
        return true;
    }

    /**
     * 设置默认地址
     * @param int $id
     * @return bool
     */
    public function setDefault(int $id)
    {
        $address = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($address->isEmpty()) {
            throw new \Exception('地址不存在');
        }

        // 先取消该用户的所有默认地址
        $this->model->where('member_id', $address['member_id'])->update(['is_default' => 0]);

        // 设置当前地址为默认
        $this->model->where([['id', '=', $id]])->update(['is_default' => 1]);

        return true;
    }

    /**
     * 生成完整地址
     * @param array $data
     * @return string
     */
    private function generateFullAddress(array $data)
    {
        $full_address = '';
        
        // 获取省市区名称
        if (!empty($data['province_id'])) {
            $province = (new \app\model\sys\SysArea())->where('id', $data['province_id'])->value('name');
            $full_address .= $province;
        }
        
        if (!empty($data['city_id'])) {
            $city = (new \app\model\sys\SysArea())->where('id', $data['city_id'])->value('name');
            $full_address .= $city;
        }
        
        if (!empty($data['district_id'])) {
            $district = (new \app\model\sys\SysArea())->where('id', $data['district_id'])->value('name');
            $full_address .= $district;
        }
        
        // 添加详细地址
        if (!empty($data['address'])) {
            $full_address .= $data['address'];
        }
        
        return $full_address;
    }

    /**
     * 获取会员选项
     * @return array
     */
    public function getMemberOptions()
    {
        return (new Member())
            ->field('member_id, username, nickname, mobile')
            ->where('status', 1)
            ->order('member_id desc')
            ->limit(1000)
            ->select()
            ->toArray();
    }
}
