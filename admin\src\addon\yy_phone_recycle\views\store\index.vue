<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">
            <!-- 搜索条件 -->
            <div class="navbar">
                <div class="navbar-left">
                    <el-input v-model="tableData.searchParam.store_name" clearable placeholder="请输入门店名称" class="input-width" />
                    <el-select v-model="tableData.searchParam.store_type" clearable placeholder="门店类型" class="input-width">
                        <el-option label="直营店" :value="1" />
                        <el-option label="加盟店" :value="2" />
                    </el-select>
                    <el-select v-model="tableData.searchParam.status" clearable placeholder="门店状态" class="input-width">
                        <el-option label="正常" :value="1" />
                        <el-option label="停用" :value="0" />
                    </el-select>
                    <el-button type="primary" @click="loadStoreList()">搜索</el-button>
                    <el-button @click="resetForm">重置</el-button>
                </div>
                <div class="navbar-right">
                    <el-button type="primary" @click="addEvent">
                        <template #icon>
                            <icon name="element-Plus" />
                        </template>
                        添加门店
                    </el-button>
                </div>
            </div>

            <!-- 门店列表 -->
            <div class="mt-[10px]">
                <el-table :data="tableData.data" size="large" v-loading="tableData.loading">
                    <template #empty>
                        <span>暂无数据</span>
                    </template>
                    <el-table-column prop="store_code" label="门店编码" min-width="120" />
                    <el-table-column prop="store_name" label="门店名称" min-width="150" />
                    <el-table-column prop="store_type" label="门店类型" min-width="100" align="center">
                        <template #default="{ row }">
                            <el-tag :type="row.store_type === 1 ? 'success' : 'primary'">
                                {{ row.store_type === 1 ? '直营店' : '加盟店' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="contact_name" label="联系人" min-width="100" />
                    <el-table-column prop="contact_phone" label="联系电话" min-width="120" />
                    <el-table-column prop="full_address" label="门店地址" min-width="200" show-overflow-tooltip />
                    <el-table-column prop="status" label="状态" min-width="80" align="center">
                        <template #default="{ row }">
                            <el-switch
                                v-model="row.status"
                                :active-value="1"
                                :inactive-value="0"
                                @change="editStatus(row)"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" fixed="right" align="right" min-width="180">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="editEvent(row)">编辑</el-button>
                            <el-button type="primary" link @click="showStaffDialog(row)">员工管理</el-button>
                            <el-button type="danger" link @click="deleteEvent(row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="mt-[16px] flex justify-end">
                    <el-pagination v-model:current-page="tableData.page" v-model:page-size="tableData.limit"
                        layout="total, sizes, prev, pager, next, jumper" :total="tableData.total"
                        @size-change="loadStoreList()" @current-change="loadStoreList" />
                </div>
            </div>
        </el-card>

        <!-- 门店编辑弹窗 -->
        <el-dialog v-model="showDialog" :title="formData.id ? '编辑门店' : '添加门店'" width="800px" :destroy-on-close="true">
            <el-form :model="formData" label-width="100px" ref="formRef" :rules="formRules">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="门店名称" prop="store_name">
                            <el-input v-model="formData.store_name" clearable placeholder="请输入门店名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="门店类型" prop="store_type">
                            <el-select v-model="formData.store_type" placeholder="请选择门店类型">
                                <el-option label="直营店" :value="1" />
                                <el-option label="加盟店" :value="2" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="联系人" prop="contact_name">
                            <el-input v-model="formData.contact_name" clearable placeholder="请输入联系人" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话" prop="contact_phone">
                            <el-input v-model="formData.contact_phone" clearable placeholder="请输入联系电话" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="门店地址" prop="address">
                    <area-select v-model:province-id="formData.province_id" v-model:city-id="formData.city_id" 
                        v-model:district-id="formData.district_id" />
                    <el-input v-model="formData.address" placeholder="请输入详细地址" class="mt-2" />
                </el-form-item>
                <el-form-item label="门店描述">
                    <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入门店描述" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showDialog = false">取消</el-button>
                <el-button type="primary" :loading="formLoading" @click="confirm">确定</el-button>
            </template>
        </el-dialog>

        <!-- 员工管理弹窗 -->
        <el-dialog v-model="showStaffDialog" title="员工管理" width="1000px" :destroy-on-close="true">
            <staff-manage v-if="showStaffDialog" :store-id="currentStore.id" @close="showStaffDialog = false" />
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { getStoreList, getStoreInfo, addStore, editStore, deleteStore, editStoreStatus } from '@/addon/yy_phone_recycle/api/store'
import StaffManage from './components/staff-manage.vue'

const showDialog = ref(false)
const showStaffDialog = ref(false)
const formLoading = ref(false)
const formRef = ref()
const currentStore = ref({})

// 表格数据
const tableData = reactive({
    data: [],
    loading: false,
    page: 1,
    limit: 10,
    total: 0,
    searchParam: {
        store_name: '',
        store_type: '',
        status: ''
    }
})

// 表单数据
const formData = reactive({
    id: '',
    store_name: '',
    store_type: 1,
    contact_name: '',
    contact_phone: '',
    province_id: '',
    city_id: '',
    district_id: '',
    address: '',
    description: ''
})

// 表单验证规则
const formRules = {
    store_name: [
        { required: true, message: '请输入门店名称', trigger: 'blur' }
    ],
    store_type: [
        { required: true, message: '请选择门店类型', trigger: 'change' }
    ],
    contact_name: [
        { required: true, message: '请输入联系人', trigger: 'blur' }
    ],
    contact_phone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
}

/**
 * 获取门店列表
 */
const loadStoreList = async (page: number = 1) => {
    tableData.loading = true
    try {
        const params = {
            page: page > 0 ? page : tableData.page,
            limit: tableData.limit,
            ...tableData.searchParam
        }
        const res = await getStoreList(params)
        tableData.data = res.data.data
        tableData.total = res.data.total
        tableData.page = res.data.page
    } catch (error) {
        console.error('获取门店列表失败:', error)
    } finally {
        tableData.loading = false
    }
}

/**
 * 重置搜索表单
 */
const resetForm = () => {
    tableData.searchParam = {
        store_name: '',
        store_type: '',
        status: ''
    }
    loadStoreList()
}

/**
 * 添加门店
 */
const addEvent = () => {
    Object.assign(formData, {
        id: '',
        store_name: '',
        store_type: 1,
        contact_name: '',
        contact_phone: '',
        province_id: '',
        city_id: '',
        district_id: '',
        address: '',
        description: ''
    })
    showDialog.value = true
}

/**
 * 编辑门店
 */
const editEvent = async (data: any) => {
    try {
        const res = await getStoreInfo(data.id)
        Object.assign(formData, res.data)
        showDialog.value = true
    } catch (error) {
        console.error('获取门店信息失败:', error)
    }
}

/**
 * 确认提交
 */
const confirm = async () => {
    try {
        await formRef.value.validate()
        formLoading.value = true
        
        if (formData.id) {
            await editStore(formData.id, formData)
            ElMessage.success('编辑成功')
        } else {
            await addStore(formData)
            ElMessage.success('添加成功')
        }
        
        showDialog.value = false
        loadStoreList()
    } catch (error) {
        console.error('操作失败:', error)
    } finally {
        formLoading.value = false
    }
}

/**
 * 删除门店
 */
const deleteEvent = (id: number) => {
    ElMessageBox.confirm('确定要删除该门店吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            await deleteStore(id)
            ElMessage.success('删除成功')
            loadStoreList()
        } catch (error) {
            console.error('删除失败:', error)
        }
    })
}

/**
 * 修改门店状态
 */
const editStatus = async (data: any) => {
    try {
        await editStoreStatus(data.id, { status: data.status })
        ElMessage.success('状态修改成功')
    } catch (error) {
        console.error('状态修改失败:', error)
        // 恢复原状态
        data.status = data.status === 1 ? 0 : 1
    }
}

/**
 * 显示员工管理弹窗
 */
const showStaffDialog = (data: any) => {
    currentStore.value = data
    showStaffDialog.value = true
}

onMounted(() => {
    loadStoreList()
})
</script>

<style lang="scss" scoped>
.input-width {
    width: 200px;
}
</style>
