<template>
    <div class="main-container">
        <!-- 搜索表单 -->
        <el-card class="box-card !border-none" shadow="never">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item label="门店名称">
                    <el-input v-model="searchForm.store_name" placeholder="请输入门店名称" clearable class="input-width" />
                </el-form-item>
                <el-form-item label="门店类型">
                    <el-select v-model="searchForm.store_type" placeholder="请选择门店类型" clearable class="input-width">
                        <el-option label="直营店" :value="1" />
                        <el-option label="加盟店" :value="2" />
                        <el-option label="合作店" :value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="searchForm.status" placeholder="请选择状态" clearable class="input-width">
                        <el-option label="正常" :value="1" />
                        <el-option label="停业" :value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="城市">
                    <el-input v-model="searchForm.city" placeholder="请输入城市" clearable class="input-width" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="loadStoreList">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 门店列表 -->
        <el-card class="box-card !border-none mt-[10px]" shadow="never">
            <template #header>
                <div class="flex justify-between items-center">
                    <span class="text-lg font-medium">门店列表</span>
                    <el-button type="primary" @click="addStore">
                        <template #icon>
                            <icon name="element-Plus" />
                        </template>
                        添加门店
                    </el-button>
                </div>
            </template>

            <el-table
                v-loading="loading"
                :data="storeList.data"
                stripe
                style="width: 100%"
            >
                <el-table-column prop="store_code" label="门店编码" width="180" show-overflow-tooltip />
                <el-table-column prop="store_name" label="门店名称" min-width="150" show-overflow-tooltip />
                <el-table-column prop="store_type" label="门店类型" width="100">
                    <template #default="{ row }">
                        <el-tag :type="getStoreTypeTagType(row.store_type)">
                            {{ getStoreTypeText(row.store_type) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="contact_name" label="联系人" width="100" show-overflow-tooltip />
                <el-table-column prop="contact_phone" label="联系电话" width="120" />
                <el-table-column label="完整地址" min-width="250" show-overflow-tooltip>
                    <template #default="{ row }">
                        <span :title="`${row.province} ${row.city} ${row.district} ${row.address}`">
                            {{ `${row.province} ${row.city} ${row.district} ${row.address}` }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="staff_count" label="员工数" width="80" align="center" />
                <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                        <el-switch
                            v-model="row.status"
                            :active-value="1"
                            :inactive-value="0"
                            @change="handleStatusChange(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="viewDetail(row)">
                            详情
                        </el-button>
                        <el-button type="primary" link @click="editStore(row)">
                            编辑
                        </el-button>
                        <el-button type="danger" link @click="handleDeleteStore(row)">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="flex justify-end mt-4">
                <el-pagination
                    v-model:current-page="storeList.page"
                    v-model:page-size="storeList.limit"
                    :total="storeList.total"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="loadStoreList"
                    @current-change="loadStoreList"
                />
            </div>
        </el-card>

        <!-- 添加/编辑门店弹窗 -->
        <el-dialog
            v-model="showEditDialog"
            :title="editingStore ? '编辑门店' : '添加门店'"
            width="900px"
            top="5vh"
            :destroy-on-close="true"
            :close-on-click-modal="false"
        >
            <store-edit
                ref="storeEditRef"
                :store-id="editingStore?.id"
                @complete="handleEditComplete"
            />
        </el-dialog>

        <!-- 门店详情弹窗 -->
        <el-dialog
            v-model="showDetailDialog"
            title="门店详情"
            width="900px"
            :destroy-on-close="true"
        >
            <store-detail
                v-if="showDetailDialog"
                :store-id="detailStoreId"
            />
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Shop, Location, View, Edit, Delete, Search, Refresh } from '@element-plus/icons-vue'
import { getStoreList, modifyStoreStatus, deleteStore } from '@/addon/yy_phone_recycle/api/store'
import StoreEdit from './info/components/store-edit.vue'
import StoreDetail from './components/store-detail.vue'

const loading = ref(false)
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const editingStore = ref(null)
const detailStoreId = ref(0)
const storeEditRef = ref()

// 搜索表单
const searchForm = reactive({
    store_name: '',
    store_type: '',
    status: '',
    city: ''
})

// 门店列表数据
const storeList = reactive({
    data: [],
    total: 0,
    page: 1,
    limit: 10
})

/**
 * 加载门店列表
 */
const loadStoreList = async () => {
    loading.value = true
    try {
        const params = {
            ...searchForm,
            page: storeList.page,
            limit: storeList.limit
        }
        const res = await getStoreList(params)
        storeList.data = res.data.data
        storeList.total = res.data.total
    } catch (error) {
        console.error('加载门店列表失败:', error)
        ElMessage.error('加载门店列表失败')
    } finally {
        loading.value = false
    }
}

/**
 * 重置搜索
 */
const resetSearch = () => {
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
    })
    storeList.page = 1
    loadStoreList()
}

/**
 * 获取门店类型文本
 */
const getStoreTypeText = (type: number) => {
    const types = {
        1: '直营店',
        2: '加盟店',
        3: '合作店'
    }
    return types[type] || '未知'
}

/**
 * 获取门店类型标签类型
 */
const getStoreTypeTagType = (type: number) => {
    const types = {
        1: 'success',
        2: 'warning',
        3: 'info'
    }
    return types[type] || 'info'
}

/**
 * 添加门店
 */
const addStore = () => {
    editingStore.value = null
    showEditDialog.value = true
}

/**
 * 编辑门店
 */
const editStore = (store: any) => {
    editingStore.value = store
    showEditDialog.value = true
}

/**
 * 编辑完成
 */
const handleEditComplete = () => {
    showEditDialog.value = false
    loadStoreList()
}

/**
 * 查看详情
 */
const viewDetail = (store: any) => {
    detailStoreId.value = store.id
    showDetailDialog.value = true
}

/**
 * 状态切换
 */
const handleStatusChange = async (store: any) => {
    try {
        await modifyStoreStatus(store.id, { status: store.status })
        ElMessage.success('状态修改成功')
    } catch (error) {
        // 恢复原状态
        store.status = store.status === 1 ? 0 : 1
        ElMessage.error('状态修改失败')
    }
}

/**
 * 删除门店
 */
const handleDeleteStore = (store: any) => {
    ElMessageBox.confirm(
        `确定要删除门店"${store.store_name}"吗？删除后不可恢复！`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(async () => {
        try {
            await deleteStore(store.id)
            loadStoreList()
        } catch (error) {
            console.error('删除失败:', error)
        }
    })
}

onMounted(() => {
    loadStoreList()
})
</script>

<style lang="scss" scoped>
.input-width {
    width: 200px;
}

.demo-form-inline {
    .el-form-item {
        margin-bottom: 16px;
    }
}
</style>
