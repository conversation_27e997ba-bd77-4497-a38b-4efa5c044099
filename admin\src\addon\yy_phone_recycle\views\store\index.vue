<template>
    <div class="main-container">
        <!-- 搜索表单 -->
        <el-card class="search-card" shadow="never">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="门店名称">
                    <el-input
                        v-model="searchForm.store_name"
                        placeholder="请输入门店名称"
                        clearable
                        class="input-width"
                        :prefix-icon="Search"
                    />
                </el-form-item>
                <el-form-item label="门店类型">
                    <el-select v-model="searchForm.store_type" placeholder="请选择门店类型" clearable class="input-width">
                        <el-option label="直营店" :value="1" />
                        <el-option label="加盟店" :value="2" />
                        <el-option label="合作店" :value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="searchForm.status" placeholder="请选择状态" clearable class="input-width">
                        <el-option label="正常" :value="1" />
                        <el-option label="停业" :value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="城市">
                    <el-input
                        v-model="searchForm.city"
                        placeholder="请输入城市"
                        clearable
                        class="input-width"
                        :prefix-icon="Location"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="loadStoreList" :icon="Search">
                        搜索
                    </el-button>
                    <el-button @click="resetSearch" :icon="Refresh">
                        重置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 门店列表 -->
        <el-card class="box-card !border-none mt-[10px]" shadow="never">
            <template #header>
                <div class="flex justify-between items-center">
                    <span class="text-lg font-medium">门店列表</span>
                    <el-button type="primary" @click="addStore">
                        <template #icon>
                            <icon name="element-Plus" />
                        </template>
                        添加门店
                    </el-button>
                </div>
            </template>

            <el-table
                v-loading="loading"
                :data="storeList.data"
                stripe
                style="width: 100%"
                :header-cell-style="{ background: '#f8f9fa', color: '#606266', fontWeight: '500' }"
                :row-style="{ height: '60px' }"
                :cell-style="{ padding: '12px 0' }"
            >
                <el-table-column prop="store_code" label="门店编码" width="140" show-overflow-tooltip>
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-tag size="small" effect="plain">{{ row.store_code }}</el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="store_name" label="门店名称" min-width="160" show-overflow-tooltip>
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <div class="store-avatar">
                                <el-icon size="20" color="#409eff">
                                    <Shop />
                                </el-icon>
                            </div>
                            <div class="ml-2">
                                <div class="font-medium text-gray-900">{{ row.store_name }}</div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="store_type" label="门店类型" width="100" align="center">
                    <template #default="{ row }">
                        <el-tag :type="getStoreTypeTagType(row.store_type)" size="small">
                            {{ getStoreTypeText(row.store_type) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="联系信息" min-width="140">
                    <template #default="{ row }">
                        <div class="contact-info">
                            <div class="text-sm font-medium text-gray-900">{{ row.contact_name }}</div>
                            <div class="text-xs text-gray-500">{{ row.contact_phone }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="地址" min-width="200" show-overflow-tooltip>
                    <template #default="{ row }">
                        <div class="address-info">
                            <el-icon size="14" color="#909399" class="mr-1">
                                <Location />
                            </el-icon>
                            <span class="text-sm text-gray-600">
                                {{ `${row.province || ''} ${row.city || ''} ${row.district || ''}` }}
                            </span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="staff_count" label="员工数" width="80" align="center">
                    <template #default="{ row }">
                        <el-tag size="small" type="info" effect="plain">
                            {{ row.staff_count || 0 }}人
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                        <el-switch
                            v-model="row.status"
                            :active-value="1"
                            :inactive-value="0"
                            active-color="#67c23a"
                            inactive-color="#f56c6c"
                            @change="handleStatusChange(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right" align="center">
                    <template #default="{ row }">
                        <div class="flex justify-center space-x-1">
                            <el-button type="primary" link size="small" @click="viewDetail(row)">
                                <el-icon class="mr-1"><View /></el-icon>
                                详情
                            </el-button>
                            <el-button type="warning" link size="small" @click="editStore(row)">
                                <el-icon class="mr-1"><Edit /></el-icon>
                                编辑
                            </el-button>
                            <el-button type="danger" link size="small" @click="handleDeleteStore(row)">
                                <el-icon class="mr-1"><Delete /></el-icon>
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination
                    v-model:current-page="storeList.page"
                    v-model:page-size="storeList.limit"
                    :total="storeList.total"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    background
                    @size-change="loadStoreList"
                    @current-change="loadStoreList"
                />
            </div>
        </el-card>

        <!-- 添加/编辑门店弹窗 -->
        <el-dialog
            v-model="showEditDialog"
            :title="editingStore ? '编辑门店' : '添加门店'"
            width="800px"
            :destroy-on-close="true"
        >
            <store-edit
                ref="storeEditRef"
                :store-id="editingStore?.id"
                @complete="handleEditComplete"
            />
        </el-dialog>

        <!-- 门店详情弹窗 -->
        <el-dialog
            v-model="showDetailDialog"
            title="门店详情"
            width="900px"
            :destroy-on-close="true"
        >
            <store-detail
                v-if="showDetailDialog"
                :store-id="detailStoreId"
            />
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Shop, Location, View, Edit, Delete, Search, Refresh } from '@element-plus/icons-vue'
import { getStoreList, modifyStoreStatus, deleteStore } from '@/addon/yy_phone_recycle/api/store'
import StoreEdit from './info/components/store-edit.vue'
import StoreDetail from './components/store-detail.vue'

const loading = ref(false)
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const editingStore = ref(null)
const detailStoreId = ref(0)
const storeEditRef = ref()

// 搜索表单
const searchForm = reactive({
    store_name: '',
    store_type: '',
    status: '',
    city: ''
})

// 门店列表数据
const storeList = reactive({
    data: [],
    total: 0,
    page: 1,
    limit: 10
})

/**
 * 加载门店列表
 */
const loadStoreList = async () => {
    loading.value = true
    try {
        const params = {
            ...searchForm,
            page: storeList.page,
            limit: storeList.limit
        }
        const res = await getStoreList(params)
        storeList.data = res.data.data
        storeList.total = res.data.total
    } catch (error) {
        console.error('加载门店列表失败:', error)
        ElMessage.error('加载门店列表失败')
    } finally {
        loading.value = false
    }
}

/**
 * 重置搜索
 */
const resetSearch = () => {
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
    })
    storeList.page = 1
    loadStoreList()
}

/**
 * 获取门店类型文本
 */
const getStoreTypeText = (type: number) => {
    const types = {
        1: '直营店',
        2: '加盟店',
        3: '合作店'
    }
    return types[type] || '未知'
}

/**
 * 获取门店类型标签类型
 */
const getStoreTypeTagType = (type: number) => {
    const types = {
        1: 'success',
        2: 'warning',
        3: 'info'
    }
    return types[type] || 'info'
}

/**
 * 添加门店
 */
const addStore = () => {
    editingStore.value = null
    showEditDialog.value = true
}

/**
 * 编辑门店
 */
const editStore = (store: any) => {
    editingStore.value = store
    showEditDialog.value = true
}

/**
 * 编辑完成
 */
const handleEditComplete = () => {
    showEditDialog.value = false
    loadStoreList()
}

/**
 * 查看详情
 */
const viewDetail = (store: any) => {
    detailStoreId.value = store.id
    showDetailDialog.value = true
}

/**
 * 状态切换
 */
const handleStatusChange = async (store: any) => {
    try {
        await modifyStoreStatus(store.id, { status: store.status })
        ElMessage.success('状态修改成功')
    } catch (error) {
        // 恢复原状态
        store.status = store.status === 1 ? 0 : 1
        ElMessage.error('状态修改失败')
    }
}

/**
 * 删除门店
 */
const handleDeleteStore = (store: any) => {
    ElMessageBox.confirm(
        `确定要删除门店"${store.store_name}"吗？删除后不可恢复！`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(async () => {
        try {
            await deleteStore(store.id)
            loadStoreList()
        } catch (error) {
            console.error('删除失败:', error)
        }
    })
}

onMounted(() => {
    loadStoreList()
})
</script>

<style lang="scss" scoped>
.input-width {
    width: 200px;
}

.main-container {
    padding: 20px;
}

.search-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border: none;
    margin-bottom: 20px;

    :deep(.el-card__body) {
        padding: 20px;
    }
}

.search-form {
    .el-form-item {
        margin-bottom: 16px;
        margin-right: 24px;

        :deep(.el-form-item__label) {
            font-weight: 500;
            color: #606266;
        }
    }

    .el-button {
        height: 36px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;

        &.el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;

            &:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }
        }
    }
}

.store-avatar {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-info {
    .text-sm {
        font-size: 14px;
        line-height: 1.4;
    }
    .text-xs {
        font-size: 12px;
        line-height: 1.3;
    }
}

.address-info {
    display: flex;
    align-items: center;
    .text-sm {
        font-size: 13px;
        line-height: 1.4;
    }
}

:deep(.el-table) {
    .el-table__header-wrapper {
        .el-table__header {
            th {
                border-bottom: 2px solid #e4e7ed;
            }
        }
    }

    .el-table__body-wrapper {
        .el-table__body {
            tr {
                &:hover {
                    background-color: #f8f9fa !important;
                }
            }
        }
    }
}

:deep(.el-card) {
    border-radius: 12px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
}

:deep(.el-button--link) {
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
        background-color: rgba(64, 158, 255, 0.1);
    }

    &.el-button--danger:hover {
        background-color: rgba(245, 108, 108, 0.1);
    }

    &.el-button--warning:hover {
        background-color: rgba(230, 162, 60, 0.1);
    }
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    padding: 20px 0;

    :deep(.el-pagination) {
        .el-pager li {
            border-radius: 6px;
            margin: 0 2px;

            &.is-active {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
        }

        .btn-prev, .btn-next {
            border-radius: 6px;
            margin: 0 2px;
        }

        .el-select .el-input {
            .el-input__wrapper {
                border-radius: 6px;
            }
        }
    }
}
</style>
