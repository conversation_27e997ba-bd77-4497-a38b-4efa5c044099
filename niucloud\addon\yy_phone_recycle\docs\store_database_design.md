# 门店回收系统数据库设计说明

## 概述
本文档描述了手机回收门店管理系统的数据库表结构设计，包含11个核心表，涵盖门店管理、员工管理、订单处理、库存管理、财务管理等核心业务功能。

## 表结构说明

### 1. 门店信息表 (www_yy_phone_recycle_store)
**用途**: 存储门店基本信息
**核心字段**:
- `store_code`: 门店编码，用于唯一标识门店
- `store_type`: 门店类型（直营店、加盟店、合作店）
- `manager_id`: 店长用户ID，关联系统用户表
- `longitude/latitude`: 经纬度坐标，用于地图定位
- `business_hours`: JSON格式存储营业时间配置
- `store_images`: JSON格式存储门店图片

### 2. 门店员工表 (www_yy_phone_recycle_store_staff)
**用途**: 管理门店员工信息和权限
**核心字段**:
- `staff_code`: 员工编号
- `role_type`: 角色类型（店长、收银员、评估师、普通员工）
- `permissions`: JSON格式存储权限配置
- `salary_type`: 薪资类型（固定工资、提成制、混合制）
- `commission_rate`: 提成比例

### 3. 门店回收订单表 (www_yy_phone_recycle_store_order)
**用途**: 记录门店回收业务订单
**核心字段**:
- `order_no`: 订单编号
- `customer_*`: 客户信息字段
- `phone_*`: 手机相关信息
- `evaluation_*`: 评估相关信息
- `estimated_price/final_price`: 预估价格和最终成交价格
- `payment_method`: 支付方式
- `order_status`: 订单状态流转
- `phone_images`: JSON格式存储手机照片
- `accessories`: JSON格式存储配件信息

### 4. 门店库存表 (www_yy_phone_recycle_store_inventory)
**用途**: 管理门店手机库存
**核心字段**:
- `phone_imei`: 手机IMEI，唯一标识
- `quality_grade`: 成色等级
- `purchase_price/estimated_sell_price`: 收购价格和预估销售价格
- `inventory_status`: 库存状态（在库、已出库、已销售、报废）
- `storage_location`: 存放位置
- `quality_check`: JSON格式存储质检记录

### 5. 门店财务记录表 (www_yy_phone_recycle_store_finance)
**用途**: 记录门店财务流水
**核心字段**:
- `finance_type`: 财务类型（收入、支出、转账）
- `category`: 分类（回收收入、销售收入、运营支出等）
- `payment_method`: 支付方式
- `voucher_images`: JSON格式存储凭证图片
- `finance_date`: 财务日期

### 6. 门店日报表 (www_yy_phone_recycle_store_daily_report)
**用途**: 门店每日经营数据统计
**核心字段**:
- `total_orders/completed_orders/cancelled_orders`: 订单统计
- `total_revenue/total_cost/gross_profit`: 财务统计
- `cash_amount/transfer_amount`: 收款方式统计
- `inventory_in/inventory_out`: 库存流转统计
- `customer_count/new_customer_count`: 客户统计
- `staff_performance`: JSON格式存储员工业绩

### 7. 门店客户表 (www_yy_phone_recycle_store_customer)
**用途**: 管理门店客户信息
**核心字段**:
- `customer_level`: 客户等级（普通、VIP、黄金、钻石）
- `total_orders/total_amount`: 客户交易统计
- `customer_source`: 客户来源
- `preferences`: JSON格式存储客户偏好

### 8. 门店设备表 (www_yy_phone_recycle_store_equipment)
**用途**: 管理门店设备资产
**核心字段**:
- `equipment_code`: 设备编码
- `equipment_type`: 设备类型（检测设备、收银设备等）
- `warranty_period/maintenance_cycle`: 保修期和维护周期
- `equipment_status`: 设备状态
- `maintenance_records`: JSON格式存储维护记录

### 9. 门店配置表 (www_yy_phone_recycle_store_config)
**用途**: 存储门店个性化配置
**核心字段**:
- `config_key/config_value`: 配置键值对
- `config_type`: 配置类型（定价、流程、通知等）
- `is_system`: 是否系统配置

### 10. 门店操作日志表 (www_yy_phone_recycle_store_log)
**用途**: 记录门店所有操作日志
**核心字段**:
- `action_type/action_name`: 操作类型和名称
- `old_data/new_data`: JSON格式存储操作前后数据
- `result/error_message`: 操作结果和错误信息

### 11. 门店通知消息表 (www_yy_phone_recycle_store_notification)
**用途**: 门店消息通知管理
**核心字段**:
- `notification_type`: 通知类型
- `priority`: 优先级
- `is_read/read_time`: 阅读状态和时间
- `expire_time`: 过期时间

## 设计特点

### 1. 数据完整性
- 使用唯一索引确保关键数据不重复
- 合理的字段约束和默认值
- 预留外键约束（可选启用）

### 2. 性能优化
- 为常用查询字段建立索引
- 使用JSON字段存储灵活配置数据
- 合理的字段类型和长度设计

### 3. 扩展性
- 预留了足够的扩展字段
- JSON字段支持灵活的业务配置
- 模块化的表结构设计

### 4. 业务适配
- 支持多种门店类型和员工角色
- 完整的订单状态流转
- 灵活的财务记录和统计
- 全面的操作日志记录

## 使用建议

1. **索引优化**: 根据实际查询需求调整索引策略
2. **数据归档**: 定期归档历史数据，保持表性能
3. **权限控制**: 结合业务需求设置合适的数据访问权限
4. **监控告警**: 建立数据监控和异常告警机制
5. **备份策略**: 制定完善的数据备份和恢复策略
