import request from '@/utils/request'

/**
 * 获取门店列表
 */
export function getStoreList(params: Record<string, any>) {
    return request.get(`yy_phone_recycle/store`, { params })
}

/**
 * 获取门店详情
 */
export function getStoreInfo(id: number) {
    return request.get(`yy_phone_recycle/store/${id}`)
}

/**
 * 添加门店
 */
export function addStore(params: Record<string, any>) {
    return request.post('yy_phone_recycle/store', params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 编辑门店
 */
export function editStore(id: number, params: Record<string, any>) {
    return request.put(`yy_phone_recycle/store/${id}`, params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 删除门店
 */
export function deleteStore(id: number) {
    return request.delete(`yy_phone_recycle/store/${id}`, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 修改门店状态
 */
export function editStoreStatus(id: number, params: Record<string, any>) {
    return request.put(`yy_phone_recycle/store/${id}/status`, params)
}

/**
 * 获取门店选项列表
 */
export function getStoreOptions() {
    return request.get('yy_phone_recycle/store/options')
}

/**
 * 获取门店员工列表
 */
export function getStoreStaff(storeId: number, params: Record<string, any>) {
    return request.get(`yy_phone_recycle/store/${storeId}/staff`, { params })
}

/**
 * 获取门店统计信息
 */
export function getStoreStatistics(storeId: number) {
    return request.get(`yy_phone_recycle/store/${storeId}/statistics`)
}

/**
 * 添加门店员工
 */
export function addStoreStaff(storeId: number, params: Record<string, any>) {
    return request.post(`yy_phone_recycle/store/${storeId}/staff`, params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 编辑门店员工
 */
export function editStoreStaff(storeId: number, staffId: number, params: Record<string, any>) {
    return request.put(`yy_phone_recycle/store/${storeId}/staff/${staffId}`, params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 删除门店员工
 */
export function deleteStoreStaff(storeId: number, staffId: number) {
    return request.delete(`yy_phone_recycle/store/${storeId}/staff/${staffId}`, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 修改员工状态
 */
export function editStoreStaffStatus(storeId: number, staffId: number, params: Record<string, any>) {
    return request.put(`yy_phone_recycle/store/${storeId}/staff/${staffId}/status`, params)
}


