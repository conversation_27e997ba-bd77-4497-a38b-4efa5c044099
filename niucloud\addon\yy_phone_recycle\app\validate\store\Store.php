<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\validate\store;

use core\base\BaseValidate;

/**
 * 门店验证器
 * Class Store
 * @package addon\yy_phone_recycle\app\validate\store
 */
class Store extends BaseValidate
{
    protected $rule = [
        'store_name' => 'require|length:1,100',
        'store_type' => 'require|in:1,2,3',
        'contact_name' => 'require|length:1,50',
        'contact_phone' => 'require|mobile',
        'province' => 'require|length:1,50',
        'city' => 'require|length:1,50',
        'district' => 'require|length:1,50',
        'address' => 'require|length:1,200',
        'status' => 'in:0,1',
    ];

    protected $message = [
        'store_name.require' => '门店名称不能为空',
        'store_name.length' => '门店名称长度不能超过100个字符',
        'store_type.require' => '门店类型不能为空',
        'store_type.in' => '门店类型值错误',
        'contact_name.require' => '联系人姓名不能为空',
        'contact_name.length' => '联系人姓名长度不能超过50个字符',
        'contact_phone.require' => '联系电话不能为空',
        'contact_phone.mobile' => '联系电话格式错误',
        'province.require' => '请选择省份',
        'province.length' => '省份长度不能超过50个字符',
        'city.require' => '请选择城市',
        'city.length' => '城市长度不能超过50个字符',
        'district.require' => '请选择区县',
        'district.length' => '区县长度不能超过50个字符',
        'address.require' => '详细地址不能为空',
        'address.length' => '详细地址长度不能超过200个字符',
        'status.in' => '状态值错误',
    ];

    protected $scene = [
        "add" => ['store_name', 'store_type', 'contact_name', 'contact_phone', 'province', 'city', 'district', 'address'],
        "edit" => ['store_name', 'store_type', 'contact_name', 'contact_phone', 'province', 'city', 'district', 'address'],
    ];
}
