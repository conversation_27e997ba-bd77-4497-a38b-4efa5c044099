<template>
    <div class="store-detail-container" v-loading="loading">
        <div v-if="storeInfo">
            <!-- 基本信息 -->
            <div class="detail-section">
                <h3 class="section-title">基本信息</h3>
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="门店名称">{{ storeInfo.store_name }}</el-descriptions-item>
                    <el-descriptions-item label="门店编码">{{ storeInfo.store_code }}</el-descriptions-item>
                    <el-descriptions-item label="门店类型">
                        <el-tag :type="getStoreTypeTagType(storeInfo.store_type)">
                            {{ getStoreTypeText(storeInfo.store_type) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="状态">
                        <el-tag :type="storeInfo.status === 1 ? 'success' : 'danger'">
                            {{ storeInfo.status === 1 ? '正常营业' : '暂停营业' }}
                        </el-tag>
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 联系信息 -->
            <div class="detail-section">
                <h3 class="section-title">联系信息</h3>
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="联系人">{{ storeInfo.contact_name }}</el-descriptions-item>
                    <el-descriptions-item label="联系电话">{{ storeInfo.contact_phone }}</el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 地址信息 -->
            <div class="detail-section">
                <h3 class="section-title">地址信息</h3>
                <el-descriptions :column="1" border>
                    <el-descriptions-item label="所在地区">
                        {{ `${storeInfo.province} ${storeInfo.city} ${storeInfo.district}` }}
                    </el-descriptions-item>
                    <el-descriptions-item label="详细地址">{{ storeInfo.address }}</el-descriptions-item>
                    <el-descriptions-item label="经纬度" v-if="storeInfo.longitude && storeInfo.latitude">
                        经度：{{ storeInfo.longitude }}，纬度：{{ storeInfo.latitude }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 营业时间 -->
            <div class="detail-section" v-if="businessHoursDisplay.length">
                <h3 class="section-title">营业时间</h3>
                <el-descriptions :column="1" border>
                    <el-descriptions-item v-for="day in businessHoursDisplay" :key="day.key" :label="day.label">
                        <span v-if="day.enabled" class="text-green-600">
                            {{ day.start }} - {{ day.end }}
                        </span>
                        <span v-else class="text-gray-400">休息</span>
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 门店图片 -->
            <div class="detail-section">
                <h3 class="section-title">门店图片
                    <span v-if="storeImages.length">({{ storeImages.length }}张)</span>
                    <span v-else class="text-gray-400">(暂无图片)</span>
                </h3>



                <div v-if="storeImages.length" class="image-gallery">
                    <div
                        v-for="(image, index) in storeImages"
                        :key="index"
                        class="image-item"
                    >
                        <el-image
                            :src="image"
                            :preview-src-list="storeImages"
                            :initial-index="index"
                            fit="cover"
                            class="store-image"
                            :preview-teleported="true"
                        >
                            <template #error>
                                <div class="image-error">
                                    <el-icon><Picture /></el-icon>
                                    <span>加载失败</span>
                                </div>
                            </template>
                        </el-image>
                        <div class="image-overlay">
                            <el-icon class="preview-icon"><ZoomIn /></el-icon>
                        </div>
                    </div>
                </div>

                <div v-else-if="storeInfo" class="no-images">
                    <el-empty description="暂无门店图片" :image-size="80">
                        <template #description>
                            <p>该门店暂未上传图片</p>
                            <p class="text-sm text-gray-400">可在编辑门店时添加图片</p>
                        </template>
                    </el-empty>
                </div>
            </div>

            <!-- 营业执照 -->
            <div class="detail-section" v-if="storeInfo.business_license">
                <h3 class="section-title">营业执照</h3>
                <div class="license-container">
                    <el-image
                        :src="img(storeInfo.business_license)"
                        :preview-src-list="[img(storeInfo.business_license)]"
                        fit="cover"
                        class="license-image"
                        :preview-teleported="true"
                    >
                        <template #error>
                            <div class="image-error">
                                <el-icon><Picture /></el-icon>
                                <span>营业执照加载失败</span>
                            </div>
                        </template>
                    </el-image>
                </div>
            </div>

            <!-- 门店描述 -->
            <div class="detail-section" v-if="storeInfo.description">
                <h3 class="section-title">门店描述</h3>
                <div class="description-content">
                    {{ storeInfo.description }}
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="detail-section">
                <h3 class="section-title">统计信息</h3>
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="员工数量">{{ storeInfo.staff_count || 0 }}</el-descriptions-item>
                    <el-descriptions-item label="排序权重">{{ storeInfo.sort || 0 }}</el-descriptions-item>
                    <el-descriptions-item label="创建时间">{{ storeInfo.create_time }}</el-descriptions-item>
                    <el-descriptions-item label="更新时间">{{ storeInfo.update_time }}</el-descriptions-item>
                </el-descriptions>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { getStoreInfo } from '@/addon/yy_phone_recycle/api/store'
import { img } from '@/utils/common'
import { Picture, ZoomIn } from '@element-plus/icons-vue'

interface Props {
    storeId: number
}

const props = defineProps<Props>()

const loading = ref(false)
const storeInfo = ref<any>(null)

// 门店图片列表
const storeImages = computed(() => {
    if (!storeInfo.value?.store_images) return []

    let images = []
    const storeImagesData = storeInfo.value.store_images

    if (Array.isArray(storeImagesData)) {
        // 如果是数组，直接使用
        images = storeImagesData.filter(img => img && img.trim())
    } else if (typeof storeImagesData === 'string') {
        if (storeImagesData.trim().startsWith('[') || storeImagesData.trim().startsWith('{')) {
            // 尝试解析JSON
            try {
                const parsed = JSON.parse(storeImagesData)
                if (Array.isArray(parsed)) {
                    images = parsed.filter(img => img && img.trim())
                }
            } catch {
                // JSON解析失败，按逗号分割
                images = storeImagesData.split(',').filter(item => item && item.trim())
            }
        } else {
            // 按逗号分割
            images = storeImagesData.split(',').filter(item => item && item.trim())
        }
    }

    // 使用img函数处理图片URL，并过滤空值
    return images.filter(image => image).map(image => img(image.trim()))
})

// 营业时间显示
const businessHoursDisplay = computed(() => {
    if (!storeInfo.value?.business_hours) return []

    let businessHours = storeInfo.value.business_hours

    // 如果是字符串，尝试解析JSON
    if (typeof businessHours === 'string') {
        try {
            businessHours = JSON.parse(businessHours)
        } catch (error) {
            console.warn('营业时间数据解析失败:', error)
            return []
        }
    }

    // 如果不是对象，返回空数组
    if (!businessHours || typeof businessHours !== 'object') {
        return []
    }

    const dayLabels = {
        monday: '周一',
        tuesday: '周二',
        wednesday: '周三',
        thursday: '周四',
        friday: '周五',
        saturday: '周六',
        sunday: '周日'
    }

    return Object.keys(dayLabels).map(key => ({
        key,
        label: dayLabels[key],
        enabled: businessHours[key]?.enabled === true,
        start: businessHours[key]?.start || '09:00',
        end: businessHours[key]?.end || '18:00'
    })).filter(day => day.enabled || !day.enabled) // 显示所有天，包括休息日
})

/**
 * 获取门店类型文本
 */
const getStoreTypeText = (type: number) => {
    const types = {
        1: '直营店',
        2: '加盟店',
        3: '合作店'
    }
    return types[type] || '未知'
}

/**
 * 获取门店类型标签类型
 */
const getStoreTypeTagType = (type: number) => {
    const types = {
        1: 'success',
        2: 'warning',
        3: 'info'
    }
    return types[type] || 'info'
}

/**
 * 加载门店信息
 */
const loadStoreInfo = async () => {
    if (!props.storeId) return
    
    loading.value = true
    try {
        const res = await getStoreInfo(props.storeId)
        storeInfo.value = res.data
    } catch (error) {
        console.error('加载门店信息失败:', error)
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    loadStoreInfo()
})
</script>

<style lang="scss" scoped>
.store-detail-container {
    .detail-section {
        margin-bottom: 30px;
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #303133;
            border-left: 4px solid #409eff;
            padding-left: 12px;
        }
    }
    
    .image-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 16px;
        margin-top: 12px;

        .image-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

                .image-overlay {
                    opacity: 1;
                }
            }

            .store-image {
                width: 100%;
                height: 120px;
                cursor: pointer;

                .image-error {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    color: #999;
                    font-size: 12px;

                    .el-icon {
                        font-size: 24px;
                        margin-bottom: 4px;
                    }
                }
            }

            .image-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;

                .preview-icon {
                    color: white;
                    font-size: 24px;
                }
            }
        }
    }
    
    .license-container {
        display: inline-block;

        .license-image {
            width: 250px;
            height: 180px;
            border-radius: 8px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            }

            .image-error {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: #999;
                font-size: 14px;

                .el-icon {
                    font-size: 32px;
                    margin-bottom: 8px;
                }
            }
        }
    }
    
    .description-content {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 8px;
        line-height: 1.6;
        color: #606266;
    }
}
</style>
