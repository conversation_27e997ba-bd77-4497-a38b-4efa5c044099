import request from '@/utils/request'

// ==================== 会员地址管理 ====================

/**
 * 获取会员地址列表
 */
export function getMemberAddressList(params: Record<string, any>) {
    return request.get('yy_phone_recycle/member/address', { params })
}

/**
 * 获取会员地址详情
 */
export function getMemberAddressInfo(id: number) {
    return request.get(`yy_phone_recycle/member/address/${id}`)
}

/**
 * 添加会员地址
 */
export function addMemberAddress(params: Record<string, any>) {
    return request.post('yy_phone_recycle/member/address', params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 编辑会员地址
 */
export function editMemberAddress(id: number, params: Record<string, any>) {
    return request.put(`yy_phone_recycle/member/address/${id}`, params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 删除会员地址
 */
export function deleteMemberAddress(id: number) {
    return request.delete(`yy_phone_recycle/member/address/${id}`, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 设置默认地址
 */
export function setDefaultMemberAddress(id: number) {
    return request.put(`yy_phone_recycle/member/address/${id}/default`, {}, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 获取会员选项
 */
export function getMemberOptions() {
    return request.get('yy_phone_recycle/member/address/member-options')
}
