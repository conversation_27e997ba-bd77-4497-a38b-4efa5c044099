import request from '@/utils/request'

/**
 * 获取回收订单列表
 */
export function getRecycleOrderList(params: Record<string, any>) {
    return request.get('yy_phone_recycle/recycle/order', { params })
}

/**
 * 获取回收订单详情
 */
export function getRecycleOrderDetail(id: number) {
    return request.get(`yy_phone_recycle/recycle/order/${id}`)
}

/**
 * 创建回收订单
 */
export function createRecycleOrder(params: Record<string, any>) {
    return request.post('yy_phone_recycle/recycle/order', params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 更新回收订单
 */
export function updateRecycleOrder(id: number, params: Record<string, any>) {
    return request.put(`yy_phone_recycle/recycle/order/${id}`, params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 取消回收订单
 */
export function cancelRecycleOrder(id: number) {
    return request.put(`yy_phone_recycle/recycle/order/${id}/cancel`, {}, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 完成回收订单
 */
export function completeRecycleOrder(id: number, params: Record<string, any>) {
    return request.put(`yy_phone_recycle/recycle/order/${id}/complete`, params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 获取手机型号列表
 */
export function getPhoneModels(params?: Record<string, any>) {
    return request.get('yy_phone_recycle/phone/model', { params })
}

/**
 * 获取手机品牌列表
 */
export function getPhoneBrands(params?: Record<string, any>) {
    return request.get('yy_phone_recycle/phone/brand', { params })
}

/**
 * 获取评估模板
 */
export function getEvaluationTemplate(modelId: number) {
    return request.get(`yy_phone_recycle/evaluation/template/${modelId}`)
}

/**
 * 开始评估
 */
export function startEvaluation(params: Record<string, any>) {
    return request.post('yy_phone_recycle/evaluation/start', params)
}

/**
 * 提交评估答案
 */
export function submitAnswer(params: Record<string, any>) {
    return request.post('yy_phone_recycle/evaluation/answer', params)
}

/**
 * 完成评估
 */
export function completeEvaluation(recordId: number) {
    return request.post(`yy_phone_recycle/evaluation/complete/${recordId}`)
}

/**
 * 获取评估记录
 */
export function getEvaluationRecord(recordId: number) {
    return request.get(`yy_phone_recycle/evaluation/record/${recordId}`)
}

/**
 * 获取评估记录列表
 */
export function getEvaluationRecordList(params: Record<string, any>) {
    return request.get('yy_phone_recycle/evaluation/record', { params })
}

/**
 * 获取门店库存列表
 */
export function getStoreInventory(params: Record<string, any>) {
    return request.get('yy_phone_recycle/store/inventory', { params })
}

/**
 * 添加库存
 */
export function addInventory(params: Record<string, any>) {
    return request.post('yy_phone_recycle/store/inventory', params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 更新库存状态
 */
export function updateInventoryStatus(id: number, params: Record<string, any>) {
    return request.put(`yy_phone_recycle/store/inventory/${id}/status`, params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 获取门店客户列表
 */
export function getStoreCustomers(params: Record<string, any>) {
    return request.get('yy_phone_recycle/store/customer', { params })
}

/**
 * 添加门店客户
 */
export function addStoreCustomer(params: Record<string, any>) {
    return request.post('yy_phone_recycle/store/customer', params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 更新门店客户
 */
export function updateStoreCustomer(id: number, params: Record<string, any>) {
    return request.put(`yy_phone_recycle/store/customer/${id}`, params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 获取门店财务记录
 */
export function getStoreFinanceRecords(params: Record<string, any>) {
    return request.get('yy_phone_recycle/store/finance', { params })
}

/**
 * 添加财务记录
 */
export function addFinanceRecord(params: Record<string, any>) {
    return request.post('yy_phone_recycle/store/finance', params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 获取门店日报
 */
export function getStoreDailyReport(params: Record<string, any>) {
    return request.get('yy_phone_recycle/store/daily-report', { params })
}

/**
 * 生成门店日报
 */
export function generateDailyReport(params: Record<string, any>) {
    return request.post('yy_phone_recycle/store/daily-report', params, { showErrorMessage: true, showSuccessMessage: true })
}

/**
 * 获取统计数据
 */
export function getRecycleStatistics(params?: Record<string, any>) {
    return request.get('yy_phone_recycle/statistics', { params })
}

/**
 * 获取门店统计数据
 */
export function getStoreStatistics(storeId: number, params?: Record<string, any>) {
    return request.get(`yy_phone_recycle/store/${storeId}/statistics`, { params })
}

/**
 * 获取门店选项
 */
export function getStoreOptions() {
    return request.get('yy_phone_recycle/store/options')
}

/**
 * 获取手机型号选项
 */
export function getPhoneModelOptions() {
    return request.get('yy_phone_recycle/phone/model/options')
}

/**
 * 获取评估模板选项
 */
export function getEvaluationTemplateOptions() {
    return request.get('yy_phone_recycle/evaluation/template/options')
}
