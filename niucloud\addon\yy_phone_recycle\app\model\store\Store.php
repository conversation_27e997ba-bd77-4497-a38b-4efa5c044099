<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yy_phone_recycle\app\model\store;

use core\base\BaseModel;

/**
 * 门店模型
 * Class Store
 * @package addon\yy_phone_recycle\app\model\store
 */
class Store extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yy_phone_recycle_store';

    /**
     * 自动写入时间戳字段
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 数据表字段信息
     * @var array
     */
    protected $schema = [
        'id' => 'int',
        'store_code' => 'string',
        'store_name' => 'string',
        'store_type' => 'int',
        'manager_id' => 'int',
        'contact_name' => 'string',
        'contact_phone' => 'string',
        'province_id' => 'int',
        'city_id' => 'int',
        'district_id' => 'int',
        'province' => 'string',
        'city' => 'string',
        'district' => 'string',
        'address' => 'string',
        'longitude' => 'decimal',
        'latitude' => 'decimal',
        'business_hours' => 'string',
        'business_license' => 'string',
        'store_images' => 'string',
        'description' => 'string',
        'status' => 'int',
        'sort' => 'int',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
    ];

    /**
     * JSON字段自动转换
     * @var array
     */
    protected $json = ['business_hours', 'store_images'];

    /**
     * 搜索器:门店名称
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchStoreNameAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('store_name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器:门店类型
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchStoreTypeAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('store_type', '=', $value);
        }
    }

    /**
     * 搜索器:状态
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('status', '=', $value);
        }
    }

    /**
     * 搜索器:城市
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchCityAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('city', '=', $value);
        }
    }

    /**
     * 获取门店类型文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStoreTypeTextAttr($value, $data)
    {
        $types = [
            1 => '直营店',
            2 => '加盟店',
            3 => '合作店'
        ];
        return $types[$data['store_type']] ?? '未知';
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        return $data['status'] == 1 ? '正常' : '停业';
    }

    /**
     * 关联店长用户
     * @return \think\model\relation\HasOne
     */
    public function manager()
    {
        return $this->hasOne('app\model\sys\SysUser', 'uid', 'manager_id')
            ->field('uid,username,real_name');
    }

    /**
     * 关联门店员工
     * @return \think\model\relation\HasMany
     */
    public function staff()
    {
        return $this->hasMany('addon\yy_phone_recycle\app\model\store\StoreStaff', 'store_id', 'id');
    }

    /**
     * 关联门店设备
     * @return \think\model\relation\HasMany
     */
    public function equipment()
    {
        return $this->hasMany('addon\yy_phone_recycle\app\model\store\StoreEquipment', 'store_id', 'id');
    }
}
